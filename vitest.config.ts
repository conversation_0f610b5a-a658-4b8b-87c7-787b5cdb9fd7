import { fileURLToPath } from 'node:url'
import { mergeConfig, defineConfig, configDefaults } from 'vitest/config'
import viteConfig from './vite.config'

export default defineConfig(({ mode, command }) => {
  const config = typeof viteConfig === 'function' ? viteConfig({ mode, command }) : viteConfig
  return mergeConfig(
    config,
    {
    test: {
      environment: 'jsdom',
      exclude: [...configDefaults.exclude, 'e2e/*'],
      root: fileURLToPath(new URL('./', import.meta.url)),
      include: ['tests/unit/**/*.{test,spec}.{js,ts,jsx,tsx}'],
      alias: {
        '@tests': fileURLToPath(new URL('./tests', import.meta.url)),
      }
    }
    }
  )
  })
