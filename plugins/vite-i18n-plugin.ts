import fs from 'fs'
import path from 'path'
import { Plugin } from 'vite'

/**
 * Vite插件：合并i18n JSON文件
 * 
 * 此插件替代了原来的gulp任务，用于将pre-task/i18n目录下的多个JSON文件
 * 合并成单个语言文件，并输出到public/data/i18n目录
 */
export default function i18nMergePlugin(): Plugin {
  return {
    name: 'vite-i18n-merger',
    enforce: 'pre', // 确保最早执行
    apply: 'serve', // 在开发和构建时都应用

    // 配置阶段就执行，保证最早生成文件
    config(config, { command }) {
      console.log('[i18n] Merging language files in config phase...')
      mergeI18nFiles()
      return config
    },

    // 在构建开始时执行
    buildStart() {
      console.log('[i18n] Merging language files...')
      mergeI18nFiles()
    },

    // 在开发服务器启动时执行
    configureServer(server) {
      // 在服务器启动前执行，确保文件已就绪
      console.log('[i18n] Initial language file merge before server start...')
      mergeI18nFiles()

      // 监视文件变化
      const watcher = server.watcher
      const i18nDir = path.resolve('pre-task/i18n')
      
      // 使用特定事件监听而非全部事件
      const handleI18nFileChange = (filePath: string) => {
        // 标准化路径，确保跨平台兼容
        const normalizedPath = filePath.replace(/\\/g, '/');
        
        if (normalizedPath.includes('pre-task/i18n') && normalizedPath.endsWith('.json')) {
          console.log(`[i18n] File changed: ${normalizedPath}`);
          mergeI18nFiles();
        }
      };
      
      // 只监听我们关心的三种事件
      watcher.on('change', handleI18nFileChange);
      watcher.on('add', handleI18nFileChange);
      watcher.on('unlink', handleI18nFileChange);
      
      try {
        // 添加主目录监听
        watcher.add(i18nDir);
        
        // 找到所有语言目录
        const langDirs = fs.readdirSync(i18nDir)
          .filter(file => {
            const fullPath = path.join(i18nDir, file);
            return fs.statSync(fullPath).isDirectory();
          });
          
        // 添加每个语言目录到监听器
        for (const langDir of langDirs) {
          const langDirPath = path.join(i18nDir, langDir);
          watcher.add(langDirPath);
          
          // 添加所有JSON文件到监听器
          const jsonFiles = fs.readdirSync(langDirPath)
            .filter(file => file.endsWith('.json'))
            .map(file => path.join(langDirPath, file));
            
          for (const jsonFile of jsonFiles) {
            watcher.add(jsonFile);
          }
        }
        
        console.log(`[i18n] Watching i18n directory: ${i18nDir}`);
      } catch (error) {
        console.error('[i18n] Error setting up watchers:', error);
      }
    }
  }
}

/**
 * 合并i18n文件的主要逻辑
 */
function mergeI18nFiles() {
  try {
    const baseDir = path.resolve('pre-task/i18n')
    const outDir = path.resolve('public/data/i18n')

    // 确保输出目录存在
    if (!fs.existsSync(outDir)) {
      fs.mkdirSync(outDir, { recursive: true })
    }

    // 获取所有语言文件夹
    const langDirs = fs.readdirSync(baseDir)
      .filter(file => {
        try {
          return fs.lstatSync(path.join(baseDir, file)).isDirectory()
        } catch (e) {
          return false
        }
      })

    // 处理每个语言文件夹
    langDirs.forEach(langDir => {
      const langPath = path.join(baseDir, langDir)
      const files = fs.readdirSync(langPath)
        .filter(file => file.endsWith('.json'))
        .map(file => path.join(langPath, file))

      // 合并JSON文件
      const mergedData = {}
      for (const file of files) {
        try {
          const fileContent = fs.readFileSync(file, 'utf8')
          const jsonData = JSON.parse(fileContent)
          // 合并对象
          Object.assign(mergedData, jsonData)
        } catch (error) {
          console.error(`[i18n] Error processing file ${file}:`, error)
        }
      }

      // 写入合并后的文件
      const outFile = path.join(outDir, `${langDir}.json`)
      fs.writeFileSync(outFile, JSON.stringify(mergedData, null, 2))
      console.log(`[i18n] Created: ${outFile}`)
    })

    console.log('[i18n] Language files merged successfully!')
  } catch (error) {
    console.error('[i18n] Error merging language files:', error)
  }
} 