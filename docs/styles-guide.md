# 样式结构和使用指南

## 样式组织结构

本项目使用了 SCSS 作为 CSS 预处理器，并将所有样式文件抽离到单独的目录中，而不是内嵌在 Vue 组件文件内。这种方式有助于提高样式的可维护性和复用性。

### 目录结构

```
src/assets/
├── css/                   # 原生CSS文件
│   ├── main.css           # Tailwind CSS 引入和基础样式
│   ├── variables.css      # CSS变量定义
│   └── ...
├── scss/                  # SCSS 样式文件
│   ├── main.scss          # 主SCSS文件，导入所有其他样式
│   ├── variables.scss     # SCSS变量、mixin等
│   ├── components/        # 组件样式
│   │   ├── pug-demo.scss       # Pug示例组件样式
│   │   ├── article-card.scss   # 文章卡片组件样式
│   │   └── ...
│   └── views/             # 页面视图样式
│       ├── home.scss      # 首页样式
│       ├── about.scss     # 关于页面样式
│       ├── article-detail.scss # 文章详情页样式
│       └── ...
└── images/                # 图片资源
```

## 使用方法

### 引入样式

在 `main.ts` 中引入主样式文件：

```ts
import './assets/css/main.css'
import './assets/scss/main.scss'
```

所有样式将通过 `main.scss` 文件导入，无需在各个组件中单独引入样式文件。

### 添加新的样式

1. **为组件创建样式**：
   - 在 `src/assets/scss/components/` 目录下创建对应的 `.scss` 文件
   - 在 `main.scss` 中导入新创建的文件

   ```scss
   // main.scss
   @import 'components/new-component';
   ```

2. **为页面创建样式**：
   - 在 `src/assets/scss/views/` 目录下创建对应的 `.scss` 文件
   - 在 `main.scss` 中导入新创建的文件

   ```scss
   // main.scss
   @import 'views/new-page';
   ```

### 样式命名规范

我们采用 BEM（Block, Element, Modifier）命名规范：

```scss
.block {
  // 块级样式
  
  &__element {
    // 元素样式
  }
  
  &--modifier {
    // 修饰符样式
  }
}
```

例如：

```scss
.article-card {
  // 文章卡片样式
  
  &__title {
    // 文章标题样式
  }
  
  &__image {
    // 文章图片样式
  }
  
  &--featured {
    // 精选文章卡片样式
  }
}
```

### 样式优先级

1. 组件级样式（特定于某个组件的样式）
2. 页面级样式（特定于某个页面的样式）
3. 全局样式（适用于整个应用的样式）

### 响应式设计

使用在 `variables.scss` 中定义的媒体查询混合宏：

```scss
@include respond-to('md') {
  // 平板及以上屏幕的样式
}

@include respond-to('lg') {
  // 桌面屏幕的样式
}
```

## 变量和主题

### 变量使用

在 `variables.scss` 中定义了常用的变量，如颜色、间距、字体等：

```scss
// 使用变量
.element {
  color: $primary-color;
  margin: $spacing-md;
  font-family: $font-family;
}
```

### CSS 变量和主题切换

主题颜色通过 CSS 变量实现，支持动态切换：

```scss
// 在组件中使用 CSS 变量
.element {
  color: var(--text-color);
  background-color: var(--bg-color);
}
```

这些 CSS 变量在亮色/暗色主题切换时会自动更新。

## 和 Pug 的结合使用

当在 Pug 模板中引用类名时，需要注意 Tailwind CSS 的特殊字符处理：

```pug
// 在 Pug 中使用类名
.container.mx-auto.px-4
  .flex.flex-col.md_flex-row.gap-4
    .w-full.md_w-1_2.p-4
      h1.text-2xl.font-bold 标题
```

注意 `md:flex-row` 在 Pug 中写作 `md_flex-row`，`w-1/2` 写作 `w-1_2`。 