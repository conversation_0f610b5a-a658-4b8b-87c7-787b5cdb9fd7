@use "@/assets/scss/variables" as *;

.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 10;
  //background-color: var(--bg-color);
  //border-bottom: 1px solid var(--border-color);
}

.layout-content {
  flex: 1;
  position: relative;
  z-index: 9;
}

.layout-footer {
  background-color: var(--bg-color);
  //border-top: 1px solid var(--border-color);
}

.page-content {
  position: relative;
  z-index: 1;
}

.section {
  padding: 6rem 0;
  position: relative;

  &.developer-section {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(240, 244, 248, 0.8) 100%);

    .dark & {
      background: linear-gradient(180deg, rgba(26, 32, 44, 0) 0%, rgba(17, 24, 39, 0.8) 100%);
    }
  }

  &.features-section {
    background: linear-gradient(180deg, rgba(240, 244, 248, 0.8) 0%, rgba(255, 255, 255, 0) 100%);

    .dark & {
      background: linear-gradient(180deg, rgba(17, 24, 39, 0.8) 0%, rgba(26, 32, 44, 0) 100%);
    }
  }
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  font-weight: 700;
  color: var(--text-color);
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);

  &:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: $primary-color;
    border-radius: 3px;
  }
}

.article-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}
