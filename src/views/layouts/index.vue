<template lang="pug">
LanguageReady
  .default-layout
    header.layout-header
      CHeader

    main.layout-content
      router-view(v-slot="{ Component }")
        transition(name="fade" mode="out-in")
          component(:is="Component")

    footer.layout-footer
      CFooter
</template>

<script setup lang="ts">
import { provide, ref } from 'vue'
import CHeader from '@/components/c-header/index.vue'
import CFooter from '@/components/c-footer/index.vue'
import LanguageReady from '@/components/language-ready/index.vue'
// 创建布局相关的状态
const isHeaderFixed = ref(false)
const layoutTheme = ref('default')

// 提供给子组件使用
provide('layout', {
  isHeaderFixed,
  layoutTheme,
  setLayoutTheme: (theme: string) => {
    layoutTheme.value = theme
  }
})
</script>

<style lang="scss" scoped>
@use './index.scss' as *;
</style>
