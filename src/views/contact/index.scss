// Palette
@use "sass:color";

$primary-color: #D11242;
$bg-body: #fdfaf6;
$bg-shape: #f4f0ec;
$text-primary: #5c4e50;
$text-secondary: #8c797e;
$accent-color: $primary-color;
$line-color: #d1c8bf;

// Base page styling
.contact-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 4rem 2rem;
  background-color: $bg-body;
  color: $text-primary;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;

  &::before, &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    z-index: 0;
  }

  &::before {
    background: $bg-shape;
    width: 120vmax;
    height: 120vmax;
    top: -60vmax;
    right: -60vmax;
  }

  &::after {
    background: color.adjust($bg-shape, $lightness: -3%);
    width: 100vmax;
    height: 100vmax;
    bottom: -50vmax;
    left: -50vmax;
  }
}

.background-curves-visual {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;

  svg path {
    stroke: $line-color;
    stroke-width: 1px;
    fill: none;
    opacity: 0.6;
  }
}

.container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

// Grid layout
.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;

  @media (min-width: 992px) {
    grid-template-columns: minmax(300px, 1.2fr) 2fr;
    gap: 5rem;
    align-items: flex-start;
  }
}

// Header
.title-wrapper {
  .title-content {
    position: sticky;
    top: 4rem;
  }

  h1 {
    font-size: clamp(3rem, 10vw, 6rem);
    font-weight: 200;
    color: $text-primary;
    letter-spacing: 1px;
    text-align: left;
    line-height: 1.1;
  }
}

// Main content area
.contact-details {
  @media (min-width: 992px) {
    padding-top: 1.5rem;
  }

  .copy-icon {
    font-size: 1.4rem;
    color: $text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: $accent-color;
    }
  }

  .contact-info {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem 4rem;
    margin-bottom: 4rem;

    .info-block {
      flex: 1 1 200px;

      h2 {
        font-size: 1.25rem;
        font-weight: 500;
        color: $text-secondary;
        margin-bottom: 2rem;
      }

      p {
        margin-bottom: 1.5rem;

        &.copy-container {
          display: flex;
          align-items: center;
          gap: 0.8rem;
        }

        a {
          display: inline-flex;
          align-items: center;
          gap: 1rem;
          text-decoration: none;
          color: $text-primary;
          font-size: 1.1rem;
          font-weight: 400;
          position: relative;
          transition: color 0.3s ease;

          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 1px;
            background-color: $accent-color;
            transition: width 0.3s ease;
          }

          &:hover {
            color: $accent-color;
            &::after {
              width: 100%;
            }
          }

          i {
            font-size: 1.3rem;
            color: $accent-color;
            transition: transform 0.3s ease;
          }

          &:hover i {
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  .address-info {
    .address-wrapper {
      display: flex;
      align-items: flex-start;
      gap: 0.8rem;
    }

    p {
      color: $text-primary;
      font-weight: 300;
      font-size: 1.1rem;
      line-height: 1.8;
      margin: 0;
    }

    .copy-icon {
      flex-shrink: 0;
      margin-top: 5px;
    }
  }
}

// Responsive Design
@media (max-width: 991px) {
  .contact-page {
    padding: 3rem 1rem;
  }

  .contact-grid {
    display: block;
  }

  .title-wrapper {
    text-align: center;
    margin-bottom: 3rem;

    .title-content {
      position: static;
    }
  }

  .contact-details {
    .contact-info {
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-bottom: 4rem;
    }

    .address-info {
      text-align: center;
    }
  }
}
