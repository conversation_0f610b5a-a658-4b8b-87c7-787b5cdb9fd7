<template lang="pug">
.contact-page
  .background-curves-visual
    svg(xmlns="http://www.w3.org/2000/svg", viewBox="0 0 1440 1024")
      path(d="M-44,114.53s128.87-211,460-158,520,380,620,380,448-232,448-232")
      path(d="M1484,808.53s-128.87,211-460,158-520-380-620-380S-44,818.53-44,818.53")

  .container
    .contact-grid
      header.title-wrapper(data-aos="fade-right")
        .title-content
          h1 {{t('app.contact.title')}}

      main.contact-details(data-aos="fade-left", data-aos-delay="200")
        section.contact-info
          article.info-block.sales
            h2 {{t('app.contact.sales')}}
            p.copy-container
              a(:href="`tel:${contactInfo.salesPhoneCountryCode}${contactInfo.salesPhone}`")
                el-icon
                  i-ep-phone-filled
                span {{`${contactInfo.salesPhoneCountryCode} ${contactInfo.salesPhone}`}}
              el-popover(placement="right" trigger="manual" :width="150" :content="t('app.contact.copySuccess')" v-model:visible="popovers.phone")
                template(#reference)
                  el-icon.copy-icon(@click="copyToClipboard(`${contactInfo.salesPhoneCountryCode} ${contactInfo.salesPhone}`, 'phone')")
                    i-ep-document-copy
            p.copy-container
              a(:href="`mailto:${contactInfo.salesEmail}`")
                el-icon
                  i-ep-message
                span {{contactInfo.salesEmail}}
              el-popover(placement="right" trigger="manual" :width="150" :content="t('app.contact.copySuccess')" v-model:visible="popovers.salesEmail")
                template(#reference)
                  el-icon.copy-icon(@click="copyToClipboard(contactInfo.salesEmail, 'salesEmail')")
                    i-ep-document-copy

          article.info-block.careers
            h2 {{t('app.contact.careers')}}
            p.copy-container
              a(:href="`mailto:${contactInfo.careersEmail}`")
                el-icon
                  i-ep-message
                span {{contactInfo.careersEmail}}
              el-popover(placement="right" trigger="manual" :width="150" :content="t('app.contact.copySuccess')" v-model:visible="popovers.careersEmail")
                template(#reference)
                  el-icon.copy-icon(@click="copyToClipboard('<EMAIL>', 'careersEmail')")
                    i-ep-document-copy

        section.address-info
          .address-wrapper
            p(v-html="t('app.contact.address')")
            el-popover(placement="top" trigger="manual" :width="150" :content="t('app.contact.copySuccess')" v-model:visible="popovers.address")
              template(#reference)
                el-icon.copy-icon(@click="copyAddress")
                  i-ep-document-copy
  StructuredData(type="breadcrumb" :data="{breadcrumb: route.meta.breadcrumb,baseUrl: 'https://www.panamericanjet.com'}")
</template>
<script setup lang="ts">
import {useI18n} from 'vue-i18n'
import {onMounted, ref} from 'vue'
import {ElMessage} from 'element-plus'
import {useRoute} from 'vue-router'
import StructuredData from '@/components/StructuredData.vue'
import {contactInfo} from '@/conf'

const {t} = useI18n()
const route = useRoute()
const popovers = ref({
  phone: false,
  salesEmail: false,
  careersEmail: false,
  address: false
})

const copyToClipboard = async (text: string, popoverKey: keyof typeof popovers.value) => {
  try {
    // 检查是否在浏览器环境中且支持clipboard API
    if (typeof navigator !== 'undefined' && navigator.clipboard) {
      await navigator.clipboard.writeText(text)
      // Reset all popovers
      Object.keys(popovers.value).forEach(key => {
        popovers.value[key as keyof typeof popovers.value] = false
      })
      // Show the specific popover
      popovers.value[popoverKey] = true
      setTimeout(() => {
        popovers.value[popoverKey] = false
      }, 2000)
    } else {
      // SSR环境或不支持clipboard API时的fallback
      console.log('Copy text:', text)
    }
  } catch (err) {
    console.error('Failed to copy: ', err)
    if (typeof ElMessage !== 'undefined') {
      ElMessage({
        message: t('app.contact.copyError'),
        type: 'error'
      })
    }
  }
}

const copyAddress = () => {
  const addressText = t('app.contact.address').replace(/<br\s*\/?>/gi, '\n')
  copyToClipboard(addressText, 'address')
}

onMounted(() => {
})
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
