// 基础信息接口
interface BaseInfo {
  icon?: string
  label: string
  value: string
}

// 布局区域接口
interface LayoutSection {
  title: string
  desc: string
  facilities: string[]
  image: string
  additionalInfo?: Record<string, any> // 额外的信息
}

// 规格组接口
interface SpecGroup {
  title: string
  items: BaseInfo[]
  additionalContent?: Record<string, any>, // 额外的内容
  [key: string]: any
}

// 主接口
export interface FleetDetail {
  id: string
  name: string
  desc: string
  planeImg?: string // 飞机主图
  exteriorImages?: string[] // 外观图集
  interiorImages?: string[] // 内饰图集
  videoUrl?: string // 视频链接

  // 基础规格组
  specs: SpecGroup
  cabin: SpecGroup

  // 布局信息
  layout: {
    title: string
    overview?: string // 布局总览图
    sections: LayoutSection[],
    [key: string]: any
  }

  // 其他可选信息
  features?: {
    title: string
    items: Array<{
      title: string
      desc: string
      icon?: string
      image?: string
    }>
  }

  advantages?: {
    title: string
    items: Array<{
      title: string
      desc: string
      icon?: string
    }>
  }

  // 技术参数
  technicalSpecs?: {
    title: string
    groups: Array<{
      title: string
      items: BaseInfo[]
    }>
  }

  // 认证信息
  certifications?: {
    title: string
    items: Array<{
      name: string
      date: string
      icon?: string
      desc?: string
    }>
  }

  // 预定信息
  booking?: {
    price?: {
      perHour?: number
      currency?: string
    }
    availability?: {
      earliest: string
      notice: string
    }
    requirements?: string[]
  }

  // 自定义扩展字段
  [key: string]: any
}
