@use "sass:color";

// New Color Palette & Variables
$primary-color: #D11242; // A sophisticated gold, kept for accents
$text-color: #555;
$title-color: #222;
$light-bg: #f8f9fa; // Light grey background for the page
$white-bg: #ffffff; // White background for content sections (cards)
$border-color: #e0e0e0;

// 水波纹动画关键帧 - 只用于 luxury-experience
@keyframes wave-rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fleet-page {
  background-color: $white-bg;
  color: $text-color;
  overflow-x: hidden;

  .section {
    padding: 5rem 0;
    border-bottom: none;

    &:nth-of-type(even) {
      background-color: $light-bg;
    }

    h2 {
      font-size: 2.8rem;
      color: $title-color;
      margin-bottom: 3rem;
      font-weight: 600;
      text-align: center;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.8;
    }

    @media (max-width: 768px) {
      padding: 2rem 0;
    }
  }

  .hero-section, .aircraft-details, .safety-section {
    .image-container {
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: auto;
        display: block;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }
    }
  }


  // Hero Section
  .hero-section {
    position: relative;
    height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-image: url("@/assets/images/fleet/webp/banner.webp");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.2); // Darker overlay for better contrast
    }

    .hero-content {
      position: relative;
      z-index: 1;
      color: white;

      h1 {
        font-size: 3.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        letter-spacing: 2px;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
      }

      .subtitle {
        font-size: 1.5rem;
        font-weight: 400;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }
    }

    @media (max-width: 768px) {
      height: 75vh;
      .hero-content {
        h1 {
          font-size: 2rem;
        }

        .subtitle {
          opacity: 1;
          font-size: 1rem;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
        }
      }
    }

    @media (max-width: 480px) {
      height: 60vh; // 更小屏幕进一步降低高度
    }
  }

  // Main Content Layout
  .main-content {
    padding: 0;
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  // Aircraft Details
  .aircraft-details {
    .el-col h2 { // Target the content heading specifically
      text-align: left;
      margin-bottom: 1.5rem;
    }

    .specs-container {
      .spec-category {
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 1.5rem;
          color: $title-color;
        }
      }

      .spec-list {
        background-color: transparent;
        list-style: none;
        padding: 0;

        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.25rem 0.5rem;
          border-bottom: 1px solid $border-color;
          border-radius: 8px;
          transition: all 0.2s ease-in-out;

          &:hover {
            background-color: #f1f3f5;
            transform: scale(1.02);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.07);
          }

          &:last-child {
            border-bottom: none;
          }

          .spec-label {
            font-size: 1rem;
            color: $text-color;
          }

          .spec-value {
            font-size: 1rem;
            color: $title-color;
            font-weight: 500;
            text-align: right;
          }
        }
      }
    }
  }

  // Safety & Luxury Sections
  .safety-section, .luxury-experience {
    .el-col {
      p {
        margin-bottom: 2rem;
      }

      .el-button {
        margin-top: 1rem;
      }
    }

    .image-container {
      .swiper {
        border-radius: 8px;
        overflow: hidden;
      }

      .swiper-slide img {
        width: 100%;
        height: 600px;
        object-fit: cover;
        @media (max-width: 768px) {
          height: 300px;
        }
      }

      :deep(.swiper-button-prev),
      :deep(.swiper-button-next) {
        color: #f8bb04;
      }

      :deep(.swiper-pagination-bullet-active) {
        background-color: #f8bb04;
      }
    }

    // Styles specific to the new luxury section layout
    &.luxury-experience {
      position: relative;
      background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
      overflow: hidden;
      padding: 2.5rem 0;

      // 水波纹动画
      .wave-container {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 0;

        .wave {
          position: absolute;
          width: 120%;
          height: 120%;
          top: -10%;
          left: -10%;
          border-radius: 45%;
          transform-origin: center center;
          animation: wave-rotate 0s linear infinite;
          pointer-events: none;

          &.wave1 {
            border: 2px solid rgba($primary-color, 0.08);
            animation-duration: 20s;
            width: 110%;
            height: 110%;
            top: -5%;
            left: -5%;
          }

          &.wave2 {
            border: 2px solid rgba($primary-color, 0.06);
            animation-duration: 25s;
            animation-direction: reverse;
            width: 115%;
            height: 115%;
            top: -7.5%;
            left: -7.5%;
          }

          &.wave3 {
            border: 2px solid rgba($primary-color, 0.04);
            animation-duration: 30s;
            width: 120%;
            height: 120%;
            top: -10%;
            left: -10%;
          }
        }
      }


      .content-wrapper {
        position: relative;
        z-index: 1;
        padding: 0 40px;
      }

      h2 {
        position: relative;
        color: #1a1a1a;
        margin-bottom: 2rem;
        text-align: center;
        font-size: 2.6rem;
        font-weight: 600;
        letter-spacing: 0.5px;

        // 标题装饰
        &::after {
          content: '';
          position: absolute;
          bottom: -0.8rem;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 1px;
          background: linear-gradient(90deg,
            transparent,
            rgba($primary-color, 0.3),
            transparent
          );
        }
      }

      .luxury-desc {
        color: #666666;
        text-align: center;
        max-width: 800px;
        margin: 0 auto 1rem;
        font-size: 1.1rem;
        line-height: 1.8;
        position: relative;
        padding: 0.8rem 2rem;
      }

      .image-container {
        position: relative;
        padding: 3rem 0;
        margin: 0 -1rem;

        .swiper {
          padding: 0 1rem;
          overflow: visible;
        }

        .swiper-slide {
          transform: scale(0.88);
          transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
          padding: 1.5rem;

          img {
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba($primary-color, 0.1);
            padding: 10px;
            background: #fff;
            position: relative;
          }

          &.swiper-slide-active {
            transform: scale(1);

            img {
              box-shadow: 0 30px 60px rgba($primary-color, 0.15);
              border-color: rgba($primary-color, 0.2);
            }
          }
        }

        :deep(.swiper-button-prev),
        :deep(.swiper-button-next) {
          color: $primary-color;
          opacity: 0.8;
          transition: all 0.3s ease;
          width: 44px;
          height: 44px;
          background: rgba(255, 255, 255, 0.95);
          border-radius: 50%;
          border: 1px solid rgba($primary-color, 0.15);

          &:hover {
            opacity: 1;
            background: white;
            box-shadow: 0 8px 20px rgba($primary-color, 0.15);
            border-color: rgba($primary-color, 0.25);
          }

          &::after {
            font-size: 18px;
          }
        }

        :deep(.swiper-pagination-bullet) {
          background: transparent;
          width: 8px;
          height: 8px;
          transition: all 0.3s ease;
          opacity: 0.8;
          border: 1px solid rgba($primary-color, 0.3);
        }

        :deep(.swiper-pagination-bullet-active) {
          background: $primary-color;
          border-color: $primary-color;
          width: 24px;
          border-radius: 4px;
        }

        // Mobile styles
        @media (max-width: 768px) {
          padding: 2rem 0;
          margin: 0 -0.5rem;

          .swiper {
            padding: 0 0.5rem;
          }

          .swiper-slide {
            padding: 0.6rem;

            img {
              border-radius: 6px;
              padding: 4px;
            }
          }

          :deep(.swiper-button-prev),
          :deep(.swiper-button-next) {
            width: 32px;
            height: 32px;

            &::after {
              font-size: 12px;
            }
          }

          :deep(.swiper-pagination) {
            bottom: -0.8rem;
          }

          :deep(.swiper-pagination-bullet) {
            width: 5px;
            height: 5px;
          }

          :deep(.swiper-pagination-bullet-active) {
            width: 16px;
          }
        }
      }
    }
  }

  // CTA Section
  .cta-section {
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    overflow: hidden;

    // 左上角装饰
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg,
        rgba($primary-color, 0.15),
        transparent
      );
    }

    // 右下角装饰
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg,
        transparent,
        rgba($primary-color, 0.15)
      );
    }

    .content-wrapper {
      position: relative;
      z-index: 1;
      padding: 4rem 2rem;

      // 左侧装饰线
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        height: 60%;
        width: 2px;
        transform: translateY(-50%);
        background: linear-gradient(180deg,
          transparent,
          rgba($primary-color, 0.1),
          transparent
        );
      }

      // 右侧装饰线
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        height: 60%;
        width: 2px;
        transform: translateY(-50%);
        background: linear-gradient(180deg,
          transparent,
          rgba($primary-color, 0.1),
          transparent
        );
      }
    }

    h2 {
      font-size: 2.4rem;
      margin-bottom: 1.5rem;
      position: relative;
      color: #1a1a1a;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -0.8rem;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg,
          transparent,
          rgba($primary-color, 0.3),
          transparent
        );
      }
    }

    p {
      margin-bottom: 2rem;
      font-size: 1.1rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      color: #666;
      line-height: 1.6;
      position: relative;
    }

    .el-button {
      transform: scale(1.1);
      transition: all 0.3s ease;
      padding: 0.8rem 2.5rem;
      font-weight: 500;
      letter-spacing: 0.5px;
      position: relative;

      &:hover {
        transform: scale(1.15);
        box-shadow: 0 8px 20px rgba($primary-color, 0.15);
      }
    }

    // 移动端适配
    @media (max-width: 768px) {
      padding: 0;
      &::before,
      &::after {
        width: 25%;
      }

      .content-wrapper {
        padding: 3rem 1.5rem;

        &::before,
        &::after {
          height: 40%;
        }
      }

      h2 {
        font-size: 1.8rem;
        margin-bottom: 1.2rem;

        &::after {
          width: 40px;
          bottom: -0.6rem;
        }
      }

      p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
      }

      .el-button {
        transform: scale(1);
        padding: 0.6rem 2rem;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }

  // Element UI Overrides
  :global(.el-button--primary) {
    background-color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: color.adjust($primary-color, $lightness: -10%);
      border-color: color.adjust($primary-color, $lightness: -10%);
    }
  }

  :global(.el-button--primary.is-plain) {
    background-color: transparent;
    color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: $primary-color;
      color: white;
    }
  }
}


// Responsive Design
@media (max-width: 992px) {
  .content-wrapper {
    padding: 0 1.5rem;
  }
  .section {
    padding: 4rem 0;

    h2 {
      font-size: 2.2rem;
      margin-bottom: 2rem;
    }
  }

  .hero-section .hero-content {
    h1 {
      font-size: 2.5rem;
    }

    .subtitle {
      font-size: 1.2rem;
    }
  }

  .safety-section .el-row, .luxury-experience .el-row {
    .el-col:first-child { // Add margin to the first column when stacked
      margin-bottom: 2rem;
    }
  }

  // Mobile: stack text on top of image
  .safety-section .el-row, .luxury-experience .el-row {
    display: flex;
    flex-direction: column;

    .el-col {
      width: 100% !important;
    }
  }
  .luxury-experience .image-col {
    order: 2;
  }
  .luxury-experience .text-col {
    order: 1;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 50vh;
  }
  .aircraft-details .specs-container {
    flex-direction: column;
    gap: 2rem;
  }
}

// Keyframes for the text shine animation
@keyframes shine-effect {
  to {
    background-position: -200% center;
  }
}

.plane-desc {
  margin-bottom: 20px;
  line-height: 1.6;
  color: var(--text-color);
}

.learn-more {
  margin-top: 24px;

  .learn-more-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    color: $primary-color;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: 4px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: $primary-color;
      transform: scaleX(0);
      transform-origin: right;
      transition: transform 0.3s ease;
    }

    .icon {
      font-size: 16px;
      transition: transform 0.3s ease;
    }

    &:hover {
      color: color.adjust($primary-color, $lightness: -10%);

      &::after {
        transform: scaleX(1);
        transform-origin: left;
      }

      .icon {
        transform: translateX(5px);
      }
    }
  }
}



