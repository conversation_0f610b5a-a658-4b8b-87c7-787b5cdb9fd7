import type {FleetDetail} from '@/views/fleet/types'
import G550Plane from '@/assets/images/fleet/plane/G550/plane.png'
import G550CabinWhole from '@/assets/images/fleet/plane/G550/cabinWhole.png'
import G550CabinH from '@/assets/images/fleet/plane/G550/cabin/head.png'
import G550CabinF from '@/assets/images/fleet/plane/G550/cabin/footer.png'
import G550Cabin1 from '@/assets/images/fleet/plane/G550/cabin/1.png'
import G550Cabin2 from '@/assets/images/fleet/plane/G550/cabin/2.png'
import G550Cabin3 from '@/assets/images/fleet/plane/G550/cabin/3.png'
import G550Cabin4 from '@/assets/images/fleet/plane/G550/cabin/4.png'
import G550Cabin5 from '@/assets/images/fleet/plane/G550/cabin/5.png'

export const G550: FleetDetail = {
  id: 'G550',
  name: 'app.fleetDetail.G550.name',
  desc: 'app.fleetDetail.G550.desc',
  planeImg: G550Plane,
  overview: {
    title: 'app.fleetDetail.G550.overview.title',
    desc: 'app.fleetDetail.G550.overview.desc',
  },
  specs: {
    title: 'app.fleetDetail.G550.specs.title',
    items: [
      {
        icon: 'i-ep-map-location',
        label: 'app.fleetDetail.G550.specs.item1Label',
        value: 'app.fleetDetail.G550.specs.item1Value'
      },
      {
        icon: 'i-ep-speedometer',
        label: 'app.fleetDetail.G550.specs.item2Label',
        value: 'app.fleetDetail.G550.specs.item2Value'
      },
      {
        icon: 'i-ep-timer',
        label: 'app.fleetDetail.G550.specs.item3Label',
        value: 'app.fleetDetail.G550.specs.item3Value'
      },
      {
        icon: 'i-ep-calendar',
        label: 'app.fleetDetail.G550.specs.item4Label',
        value: 'app.fleetDetail.G550.specs.item4Value'
      }
    ]
  },
  cabin: {
    title: 'app.fleetDetail.G550.cabin.title',
    items: [
      {
        icon: 'i-ep-user-filled',
        label: 'app.fleetDetail.G550.cabin.item1Label',
        value: 'app.fleetDetail.G550.cabin.item1Value'
      },
      {
        icon: 'i-ep-moon',
        label: 'app.fleetDetail.G550.cabin.item2Label',
        value: 'app.fleetDetail.G550.cabin.item2Value'
      },
      {
        icon: 'i-ep-suitcase',
        label: 'app.fleetDetail.G550.cabin.item3Label',
        value: 'app.fleetDetail.G550.cabin.item3Value'
      }
    ]
  },
  layout: {
    title: 'app.fleetDetail.G550.layout.title',
    facilitiesTitle: 'app.fleetDetail.G550.layout.facilitiesTitle',
    img: G550CabinWhole,
    sections: [
      {
        title: 'app.fleetDetail.G550.layout.section1Title',
        desc: 'app.fleetDetail.G550.layout.section1Desc',
        facilities: ['app.fleetDetail.G550.layout.section1Facilities1', 'app.fleetDetail.G550.layout.section1Facilities2', 'app.fleetDetail.G550.layout.section1Facilities3'],
        image: G550Cabin1
      },
      {
        title: 'app.fleetDetail.G550.layout.section2Title',
        desc: 'app.fleetDetail.G550.layout.section2Desc',
        facilities: ['app.fleetDetail.G550.layout.section2Facilities1', 'app.fleetDetail.G550.layout.section2Facilities2'],
        image: G550Cabin2
      },
      {
        title: 'app.fleetDetail.G550.layout.section3Title',
        desc: 'app.fleetDetail.G550.layout.section3Desc',
        facilities: ['app.fleetDetail.G550.layout.section3Facilities1', 'app.fleetDetail.G550.layout.section3Facilities2', 'app.fleetDetail.G550.layout.section3Facilities3'],
        image: G550Cabin3
      },
      {
        title: 'app.fleetDetail.G550.layout.section4Title',
        desc: 'app.fleetDetail.G550.layout.section4Desc',
        facilities: ['app.fleetDetail.G550.layout.section4Facilities1', 'app.fleetDetail.G550.layout.section4Facilities2', 'app.fleetDetail.G550.layout.section4Facilities3'],
        image: G550Cabin4
      },
      {
        title: 'app.fleetDetail.G550.layout.section5Title',
        desc: 'app.fleetDetail.G550.layout.section5Desc',
        facilities: ['app.fleetDetail.G550.layout.section5Facilities1', 'app.fleetDetail.G550.layout.section5Facilities2', 'app.fleetDetail.G550.layout.section5Facilities3'],
        image: G550Cabin5
      }
    ],
    cabinHead: G550CabinH,
    cabinFooter: G550CabinF
  }
}
