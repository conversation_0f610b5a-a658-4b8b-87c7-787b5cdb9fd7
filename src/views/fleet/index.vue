<template lang="pug">
.fleet-page
  // Hero Section
  section.hero-section(data-aos="fade-in")
    .hero-content
      h1(data-aos="fade-down" data-aos-delay="300") {{t(hero.title)}}
      p.subtitle(data-aos="fade-up" data-aos-delay="500") {{t(hero.subtitle)}}
  // Main Content
  .main-content#mainContent
    // Aircraft Details Section
    section.section.aircraft-details(data-aos="fade-up")
      .content-wrapper
        el-row(:gutter="40" align="middle")
          el-col(:xs="24" :lg="12")
            .image-container
              img(:src="planeImg" alt="Pan American Jet(泛美)")
          el-col(:xs="24" :lg="12")
            h2 {{t(plane.name)}}
            p.plane-desc {{t(plane.desc)}}
            .learn-more
              router-link.learn-more-link(:to="{name:'fleetDetail', params:{id:plane.id}}" target="_blank")
                span {{t('app.fleet.plane.learnMore')}}
                el-icon.icon
                  i-ep-arrow-right
        .specs-container
          .spec-category(v-for="item in plane.detail" )
            h3 {{t(item.name)}}
            ul.spec-list
              li(v-for="sItem in item.children" )
                span.spec-label {{t(sItem.label)}}
                span.spec-value {{t(sItem.value)}}
    // Luxury Experience Section
    section.section.luxury-experience(data-aos="fade-up")
      .wave-container
        .wave.wave1
        .wave.wave2
        .wave.wave3
      .content-wrapper
        h2 {{t(luxury.title)}}
        p.luxury-desc {{t(luxury.desc)}}
        .image-container
          swiper(
            :modules="swiperModules"
            :centeredSlides="true"
            :slides-perView="2"
            :loop="true"
            :pagination="{ clickable: true }"
            :navigation="true"
            :autoplay="{ delay: 3000, disableOnInteraction: false }",
            :coverflowEffect="coverflowEffectSetting"
            effect="coverflow"
          )
            swiper-slide(v-for="(img, index) in luxury.image" :key="index")
              img(:src="img" alt="Luxury Cabin Interior" loading="lazy" draggable="false")
    // Safety and Professionalism Section
    section.section.safety-section(data-aos="fade-up")
      .content-wrapper
        h2 {{t(safety.title)}}
        el-row(:gutter="60" align="middle")
          el-col(:xs="24" :md="12")
            .image-container
              img(:src="safety.image" alt="Professional Pilots")
          el-col(:xs="24" :md="12")
            p {{t(safety.desc)}}
            //el-button(type="primary" plain) {{safety.btn}}
    // CTA Section
    section.section.cta-section(data-aos="zoom-in")
      .content-wrapper
        h2 {{t(cta.title)}}
        p {{t(cta.subtitle)}}
        el-button(type="primary" size="large" @click="handleContact") {{t(cta.cta)}}
  StructuredData(type="breadcrumb" :data="{breadcrumb: route.meta.breadcrumb,baseUrl: 'https://www.panamericanjet.com'}")
</template>
<script setup lang="ts">
import {onMounted, ref} from 'vue'
import AOS from 'aos'
import 'aos/dist/aos.css'
import {Swiper, SwiperSlide} from 'swiper/vue'
import {Navigation, Pagination, Autoplay, EffectCoverflow} from 'swiper/modules'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-coverflow'
import safetyImg from '@/assets/images/fleet/webp/safety.webp'
import luxuryImg1 from '@/assets/images/fleet/webp/cabin/cabin1.webp'
import luxuryImg2 from '@/assets/images/fleet/webp/cabin/cabin2.webp'
import luxuryImg3 from '@/assets/images/fleet/webp/cabin/cabin3.webp'
import luxuryImg4 from '@/assets/images/fleet/webp/cabin/cabin4.webp'
import luxuryImg5 from '@/assets/images/fleet/webp/cabin/cabin5.webp'
import planeImg from '@/assets/images/fleet/banner.png'
import {useI18n} from 'vue-i18n'
// import {useHeaderScroll} from '@/composables/useHeaderScroll'
import {useRouter, useRoute} from 'vue-router'
import StructuredData from '@/components/StructuredData.vue'

const router = useRouter()
const route = useRoute()
const {t} = useI18n()
// Use the composable to handle header scroll effect
// useHeaderScroll('#mainContent')

const swiperModules = [Navigation, Pagination, Autoplay, EffectCoverflow]
const hero = ref({
  title: 'app.fleet.banner.title',
  subtitle: 'app.fleet.banner.subtitle'
})
const plane = ref({
  id: 'G550',
  name: 'app.fleet.plane.name',
  desc: 'app.fleet.plane.desc',
  detail: [
    {
      name: 'app.fleet.plane.performanceParameters',
      children: [
        {
          label: 'app.fleet.plane.voyageLabel',
          value: 'app.fleet.plane.voyageValue'
        },
        {
          label: 'app.fleet.plane.speedLabel',
          value: 'app.fleet.plane.speedValue'
        },
        {
          label: 'app.fleet.plane.durationLabel',
          value: 'app.fleet.plane.durationValue'
        }
      ]
    },
    {
      name: 'app.fleet.plane.cabinSpecifications',
      children: [
        {
          label: 'app.fleet.plane.passengerLabel',
          value: 'app.fleet.plane.passengerValue'
        },
        {
          label: 'app.fleet.plane.bunkLabel',
          value: 'app.fleet.plane.bunkValue'
        },
        {
          label: 'app.fleet.plane.volumeLabel',
          value: 'app.fleet.plane.volumeValue'
        }
      ]
    }
  ]
})
const safety = ref({
  title: 'app.fleet.safety.title',
  desc: 'app.fleet.safety.desc',
  btn: 'app.fleet.safety.btn',
  image: safetyImg
})
const luxury = ref({
  title: 'app.fleet.luxury.title',
  desc: 'app.fleet.luxury.desc',
  image: [luxuryImg1, luxuryImg2, luxuryImg3, luxuryImg4, luxuryImg5]
})
const cta = ref({
  title: 'app.fleet.cta.title',
  subtitle: 'app.fleet.cta.subtitle',
  cta: 'app.fleet.cta.btn'
})
const coverflowEffectSetting = ref({
  rotate: 50,
  stretch: 0,
  depth: 100,
  modifier: 1,
  slideShadows: true
})
onMounted(() => {
  AOS.init({
    duration: 800,
    once: true,
    offset: 100
  })
})

function handleContact () {
  router.push({name: 'contact'})
}
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
