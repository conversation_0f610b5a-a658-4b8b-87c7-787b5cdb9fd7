@use "sass:color";

// Variables
$primary-color: #D11242;
$text-color: #333;
$light-text-color: #666;
$background-color: #f8f9fa;
$white-color: #fff;



.fleet-detail-page {
  background-color: $white-color;
  color: $text-color;
  width: 100%;
  min-height: 100vh;
  position: relative;
}

// Hero Section
.hero {
  position: relative;
  height: auto;
  min-height: 45vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  width: 100%;
  margin-top: 80px;
  padding: 0 20px 30px;
  box-sizing: border-box;
  .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    box-sizing: border-box;

    @media (max-width: 768px) {
      padding: 0;
    }

    .hero-title {
      font-size: 3.8rem;
      font-weight: 600;
      color: #1C3854;
      margin: 0 0 35px;
      letter-spacing: 2px;
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 1s ease forwards;
      position: relative;
      word-break: break-word;

      @media (max-width: 768px) {
        font-size: 2.3rem;
        padding: 0 15px;
        margin: 0 0 25px;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(to right,
          transparent,
          rgba(28, 56, 84, 0.3) 20%,
          rgba(28, 56, 84, 0.3) 80%,
          transparent
        );
      }
    }

    .hero-image {
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      padding: 0 15px;
      box-sizing: border-box;
      position: relative;

      .el-image {
        display: block;
        width: 90%;
        height: auto;
        max-height: 260px;
        margin: 0 auto;
        object-fit: contain;
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.12));
        opacity: 0;
        transform: translateX(-100%) translateY(30px) rotate(-5deg);
        animation: flyIn 1.4s ease-out 0.3s forwards,
                  flying 6s ease-in-out infinite 1.7s;
        transform-origin: center center;
        will-change: transform, opacity;
      }

      // 添加飞机阴影
      &::after {
        content: '';
        position: absolute;
        bottom: -20px;
        left: 50%;
        width: 60%;
        height: 10px;
        background: rgba(0, 0, 0, 0.25);
        border-radius: 50%;
        filter: blur(8px);
        transform: translateX(-50%) scale(0.8);
        z-index: -1;
        opacity: 0;
        animation: shadowMove 6s ease-in-out infinite,
                  shadowFadeIn 0.6s ease 1.2s forwards;
      }

      @media (max-width: 768px) {
        max-width: 100%;
        padding: 0 15px;

        .el-image {
          width: 85%;
          max-height: 180px;
          transform: translateX(-80%) translateY(20px) rotate(-3deg) scale(0.8);
          animation: flyIn 1.4s ease-out 0.3s forwards,
                    flyingMobile 4s ease-in-out infinite 1.7s;
        }

        &::after {
          width: 50%;
          height: 8px;
          bottom: -15px;
          animation: shadowMoveMobile 4s ease-in-out infinite,
                    shadowFadeIn 0.6s ease 1.2s forwards;
        }
      }
    }
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 入场飞行动画
  @keyframes flyIn {
    0% {
      opacity: 0;
      transform: translateX(-80%) translateY(30px) rotate(-5deg) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translateX(0) translateY(0) rotate(-10deg) scale(1);
    }
  }

  // 阴影淡入动画
  @keyframes shadowFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  // 飞行动画
  @keyframes flying {
    0%, 100% {
      transform: translateY(0) rotate(-10deg);
    }
    50% {
      transform: translateY(-20px) rotate(-12deg);
    }
  }

  // 添加阴影动画
  @keyframes shadowMove {
    0%, 100% {
      transform: translateX(-50%) scale(0.8);
      opacity: 0.8;
    }
    50% {
      transform: translateX(-50%) scale(0.6);
      opacity: 0.6;
    }
  }

  // 移动端飞行动画
  @keyframes flyingMobile {
    0%, 100% {
      transform: translateY(0) rotate(-10deg);
    }
    50% {
      transform: translateY(-15px) rotate(-12deg);
    }
  }

  // 移动端阴影动画
  @keyframes shadowMoveMobile {
    0%, 100% {
      transform: translateX(-50%) scale(0.7);
      opacity: 0.7;
    }
    50% {
      transform: translateX(-50%) scale(0.5);
      opacity: 0.5;
    }
  }

  @media (min-width: 1600px) {
    margin-top: 140px;

    .hero-content {
      .hero-title {
        font-size: 4.5rem;

        &::after {
          width: 100px;
        }
      }

      .hero-image {
        max-width: 900px;

        .el-image {
          max-height: 320px;
        }
      }
    }
  }
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  box-sizing: border-box;
  width: 100%;

  @media (max-width: 768px) {
    padding: 20px 15px;
  }
}

// Section Common Styles
.section-title {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 50px;
  color: $text-color;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: $primary-color;
  }
}

// Overview Section
.overview-section {
  margin: 0 0 40px;
  padding: 0 0 40px 0;
  position: relative;
  width: 100%;
  box-sizing: border-box;

  .section-title {
    font-size: 2.8rem;
    margin-bottom: 40px;
    text-align: left;
    padding-left: 5%;

    &::after {
      left: 5%;
      transform: none;
      width: 80px;
      height: 4px;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
      margin-bottom: 30px;
    }
  }

  .plane-overview {
    position: relative;
    margin: 0;
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 60px;
    align-items: center;
    padding: 0 5%;
    box-sizing: border-box;
    width: 100%;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 55%;
      width: 1px;
      height: 70%;
      background: linear-gradient(to bottom,
        transparent,
        rgba($text-color, 0.1) 20%,
        rgba($text-color, 0.1) 80%,
        transparent
      );
      transform: translateY(-50%);
    }

    @media (max-width: 1200px) {
      gap: 40px;

      &::before {
        left: 50%;
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 30px;
      padding: 0 15px;

      &::before {
        display: none;
      }
    }

    .overview-image {
      position: relative;
      height: auto;
      min-height: 250px;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      transform-style: preserve-3d;
      perspective: 1000px;
      order: 1;

      .el-image {
        width: 110%;
        height: auto;
        object-fit: contain;
        transform: translateX(5%) rotateY(2deg);
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.1));

        &:hover {
          transform: translateX(5%) rotateY(0deg) scale(1.02);
        }
      }

      @media (max-width: 768px) {
        min-height: 200px;
        padding: 15px;
        order: 2;

        .el-image {
          width: 100%;
          transform: none;
          filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));

          &:hover {
            transform: none;
          }
        }
      }
    }

    .overview-content {
      position: relative;
      padding: 0;
      order: 2;

      .overview-desc {
        font-size: 1.2rem;
        line-height: 2;
        color: $text-color;
        margin: 0;
        position: relative;
        padding-left: 30px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 12px;
          width: 15px;
          height: 2px;
          background-color: $primary-color;
        }
      }

      @media (max-width: 768px) {
        order: 1;
        .overview-desc{
            padding-left: 0;
            &::before{
                display: none;
            }
        }
      }
    }
  }

  .specs-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
    background: #fff;
    margin-top: 40px;

    .specs-card {
      padding: 0;
      box-shadow: none;
      background: none;

      .specs-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 30px;
        color: $text-color;
        position: relative;
        padding-bottom: 15px;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 40px;
          height: 3px;
          background-color: $primary-color;
        }
      }

      .specs-list {
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .spec-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid rgba($text-color, 0.1);
        background: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        cursor: pointer;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          padding-left: 10px;
          padding-right: 10px;
          background-color: #f1f3f5;
          transform: scale(1.02);
          box-shadow: 0 6px 15px rgba(0, 0, 0, 0.07);


          .spec-content {
            .spec-label {
              color: $primary-color;
            }

            .spec-value {
              transform: scale(1.05);
              color: $primary-color;
            }
          }
        }

        .spec-icon {
          display: none;
        }

        .spec-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          position: relative;
          z-index: 1;
        }

        .spec-label {
          font-size: 1.1rem;
          color: $text-color;
          margin: 0;
          order: 1;
          transition: color 0.3s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background-color: $primary-color;
            transition: width 0.3s ease;
          }
        }

        &:hover .spec-label::after {
          width: 100%;
        }

        .spec-value {
          font-size: 1.1rem;
          font-weight: 500;
          color: $text-color;
          margin: 0;
          order: 2;
          text-align: right;
          transition: all 0.3s ease;
          transform-origin: right center;
        }
      }
    }

    @media (max-width: 768px) {
      margin-top: 40px;

      .specs-card {
        .specs-title {
          font-size: 1.8rem;
          margin-bottom: 20px;
        }

        .spec-item {
          padding: 12px 0;

          &:hover {
            padding-left: 5px;

            .spec-content {
              .spec-value {
                transform: scale(1.02);
              }
            }
          }

          .spec-label,
          .spec-value {
            font-size: 1rem;
          }
        }
      }
    }
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Add animation to each spec item
.specs-list {
  .spec-item {
    animation: fadeInLeft 0.5s ease forwards;
    opacity: 0;

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }
}

// Layout Section
.layout-section {
  .layout-nav {
    margin-bottom: 30px;

    .button-container {
      display: flex;
      gap: 15px;
      justify-content: center;

      &.mobile {
        justify-content: flex-start;
        overflow-x: auto;
        margin: 0 -10px; // 抵消父容器的padding
        padding: 0 10px 5px; // 添加padding使两端有间距,为滚动条留出空间
        -webkit-overflow-scrolling: touch; // 增强移动端滚动体验

        /* 隐藏滚动条但保持功能 */
        &::-webkit-scrollbar {
          display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;

        .el-button {
          flex: 0 0 auto; // 防止按钮被压缩
          white-space: nowrap; // 防止文字换行
          min-width: fit-content; // 根据内容自适应宽度
        }
      }
    }
  }

  .layout-combined {
    display: flex;
    gap: 40px;
    margin-top: 40px;

    .layout-content-wrapper {
      flex: 1;
      min-width: 0; // 防止flex子项溢出

      .layout-item {
        background: rgba(white, 0.5);
        border-radius: 12px;
        margin-bottom: 20px;

        .layout-content {
          height: auto;
        }
      }
    }

    .layout-image-wrapper {
      width: 200px;
      height: calc(100vh - 200px);
      flex-shrink: 0;
      background: $background-color;
      border-radius: 12px;
      padding: 15px;



      // 禁止所有图片拖拽
      img {
        user-select: none;
        -webkit-user-drag: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      .cabin-structure {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: rgba($primary-color, 0.05);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba($primary-color, 0.2);
          border-radius: 2px;

          &:hover {
            background: rgba($primary-color, 0.3);
          }
        }

        .cabin-head {
          width: 100%;
          flex-shrink: 0;
          padding: 5px;
          background: rgba(white, 0.5);
          border-radius: 8px 8px 0 0;

          .el-image {
            width: 100%;
            display: block;
            opacity: 0.8;
          }
        }

        .cabin-body-container {
          width: 100%;
          background: rgba(white, 0.5);
          margin: 1px 0;

          .cabin-body {
            padding: 5px;

            .section-image {
              width: 100%;
              margin-bottom: 1px;
              transition: all 0.3s ease;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.65);
                transition: opacity 0.3s ease;
                z-index: 1;
                pointer-events: none;
              }

              &.active {
                transform: scale(1.02);

                &::before {
                  opacity: 0;
                }

                .el-image {
                  filter: brightness(1) contrast(1);
                  filter: drop-shadow(0 0 8px rgba($primary-color, 0.3));
                }
              }

              &:last-child {
                margin-bottom: 0;
              }

              .el-image {
                width: 100%;
                display: block;
                position: relative;
                z-index: 0;
                transition: all 0.3s ease;
                filter: brightness(1.2) contrast(0.8);
                opacity: 0.8;
                cursor: pointer;
                &:hover{
                  transform: scale(1.02);
                }
              }
            }
          }
        }

        .cabin-footer {
          width: 100%;
          flex-shrink: 0;
          padding: 5px;
          background: rgba(white, 0.5);
          border-radius: 0 0 8px 8px;

          .el-image {
            width: 100%;
            display: block;
            opacity: 0.8;
          }
        }
      }
    }

    // 移动端样式调整
    &.mobile {
      flex-direction: row !important; // 强制保持水平布局
      gap: 15px;
      padding: 0 10px;
      margin-top: 20px;

      .layout-content-wrapper {
        flex: 0 0 55%; // 固定宽度比例
        min-width: 0; // 允许内容收缩

        .layout-item {
          padding: 12px;
          margin-bottom: 15px;

          .layout-title {
            font-size: 1.2rem;
          }

          .layout-desc {
            font-size: 0.9rem;
            margin: 12px 0;
            line-height: 1.4;
          }
        }

        .facilities {
          padding: 12px;
          margin-bottom: 0; // 移除最后一个元素的底部间距

          .facilities-title {
            font-size: 1rem;
            margin-bottom: 10px;
          }

          .facility-list {
            grid-template-columns: 1fr;
            gap: 8px;
          }

          .facility-item {
            padding: 4px 8px;
            font-size: 0.85rem;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .layout-image-wrapper {
        height: calc(100vh - 150px);
        flex: 0 0 45%; // 固定宽度比例
        min-width: 0; // 允许内容收缩
        padding: 8px;
      }
    }
  }

  .layout-details {
    height: 100%;

    .layout-item {
      height: 100%;
      background: #fff;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
    }

    .layout-content {
      height: 100%;
      display: flex;
      flex-direction: column;

      .layout-header {
        margin-bottom: 25px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -12px;
          left: 0;
          width: 100%;
          height: 1px;
          background: linear-gradient(to right,
            rgba($primary-color, 0.2),
            rgba($primary-color, 0.1) 50%,
            transparent
          );
        }
      }

      .layout-title {
        font-size: 2rem;
        font-weight: 600;
        color: $text-color;
        margin-bottom: 0;
        letter-spacing: 0.5px;
      }

      .layout-line {
        display: none; // Hide since we're using the gradient line
      }

      .layout-desc {
        font-size: 1.1rem;
        line-height: 1.8;
        color: $light-text-color;
        margin: 25px 0 35px;
        letter-spacing: 0.3px;
      }
    }
  }

  .facilities {
    background: rgba($primary-color, 0.03);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid rgba($primary-color, 0.08);

    .facilities-title {
      font-size: 1.3rem;
      font-weight: 500;
      margin-bottom: 20px;
      color: $text-color;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: $primary-color;
        border-radius: 2px;
      }
    }

    .facility-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 16px;
      padding: 0 5px;
    }

    .facility-item {
      display: flex;
      align-items: center;
      gap: 12px;
      color: $text-color;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba($primary-color, 0.05);
      }

      .el-icon {
        color: $primary-color;
        flex-shrink: 0;
        font-size: 18px;
      }

      span {
        font-size: 1.05rem;
        line-height: 1.5;
      }
    }
  }
}

// Responsive Styles
@media (max-width: 768px) {
  .hero {
    min-height: 300px;
    background-attachment: scroll;
    margin-top: 100px;
    .hero-title {
      font-size: 2.8rem;
      letter-spacing: 2px;
    }
  }

  .page-content {
    padding: 0 15px 40px;
  }

  .section-title {
    font-size: 1.8rem;
    margin-bottom: 30px;
  }

  .overview-section {
    .specs-grid {
      grid-template-columns: 1fr;
    }

    .specs-card {
      padding: 20px;

      .specs-title {
        font-size: 1.4rem;
        margin-bottom: 20px;
      }
    }

    .spec-item {
      padding: 12px;
    }

    .spec-value {
      font-size: 1.1rem;
    }
  }

  .layout-section {
    .layout-combined {
      flex-direction: column;
      gap: 30px;
      min-height: auto;

      .layout-content-wrapper {
        flex: 1;
        min-width: 0;
      }

      .layout-image-wrapper {
        width: 100%;
        padding: 15px;
        height: calc(100vh - 300px);
      }
    }

    .layout-item {
      margin-bottom: 50px;
    }

    .layout-content {
      padding: 20px 0;

      .layout-title {
        font-size: 1.5rem;
      }

      .layout-desc {
        font-size: 1rem;
      }
    }

    .facilities {
      padding: 20px;

      .facilities-title {
        font-size: 1.2rem;
        margin-bottom: 15px;
      }

      .facility-list {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .facility-item {
        padding: 6px 10px;

        span {
          font-size: 1rem;
        }
      }
    }
  }
}

// 超小屏幕适配
@media screen and (max-width: 380px) {
  .layout-combined.mobile {
    gap: 10px;
    padding: 0 5px;

    .layout-content-wrapper {
      flex: 0 0 60%;

      .layout-item {
        padding: 10px;
        margin-bottom: 10px;

        .layout-title {
          font-size: 1.1rem;
        }

        .layout-desc {
          font-size: 0.85rem;
        }
      }
    }

    .layout-image-wrapper {
      flex: 0 0 40%;
    }
  }
}
