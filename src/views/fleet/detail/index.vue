<template lang="pug">
.fleet-detail-page(v-if="planeData")
    //- Hero Section
    section.hero
      .hero-content
        h1.hero-title {{ t(planeData.name) }}
        .hero-image
          el-image(
            :src="planeData.planeImg"
            fit="contain"
            loading="lazy"
            draggable="false"
          )

    .page-content
      //- Overview Section
      section.overview-section
        h2.section-title(data-aos="fade-up") {{ t(planeData.overview.title) }}

        //- Plane Overview
        .plane-overview
          .overview-content(
            :data-aos="isMobile ? 'fade-up' : 'fade-right'"
            :data-aos-duration="isMobile ? 600 : 1000"
          )
            p.overview-desc {{ t(planeData.overview.desc) }}
          .overview-image(
            :data-aos="isMobile ? 'fade-up' : 'fade-left'"
            :data-aos-duration="isMobile ? 600 : 1000"
            :data-aos-delay="isMobile ? 0 : 200"
          )
            el-image(
              :src="planeData.planeImg"
              fit="cover"
              loading="lazy"
              draggable="false"
            )

        //- Specs Grid
        .specs-grid
          //- Performance Specs
          .specs-card(data-aos="fade-up")
            h3.specs-title {{ t(planeData.specs.title) }}
            .specs-list
              .spec-item(
                v-for="spec in planeData.specs.items"
                :key="spec.label"
              )
                .spec-content
                  .spec-label {{ t(spec.label) }}
                  .spec-value {{ t(spec.value) }}

          //- Cabin Specs
          .specs-card(
            data-aos="fade-up"
            :data-aos-delay="isMobile ? 0 : 200"
          )
            h3.specs-title {{ t(planeData.cabin.title) }}
            .specs-list
              .spec-item(
                v-for="item in planeData.cabin.items"
                :key="item.label"
              )
                .spec-content
                  .spec-label {{ t(item.label) }}
                  .spec-value {{ t(item.value) }}

      //- Cabin Layout Section
      section.layout-section(ref="layoutSectionRef")
        h2.section-title(data-aos="fade-up") {{ t(planeData.layout.title) }}

        //- Layout Navigation Buttons
        .layout-nav.text-center(data-aos="fade-up")
          .button-container(:class="{ 'mobile': isMobile }")
            el-button(
              v-for="(section, index) in planeData.layout.sections"
              :key="section.title"
              :type="selectedSection === index ? 'primary' : 'default'"
              :size="isMobile ? 'default' : 'large'"
              @click="handleNavClick(index)"
              :class="{ 'full-width': isMobile }"
            ) {{ t(section.title) }}

        //- Combined Layout Section
        .layout-combined(:class="{ 'mobile': isMobile }")
          //- Layout Details
          .layout-content-wrapper
            .layout-details(:data-aos="isMobile ? 'fade-up' : 'fade-right'" :data-aos-duration="isMobile ? 600 : 1000"
              :data-aos-delay="isMobile ? 0 : 200")
              .layout-item(
                v-for="(section, index) in planeData.layout.sections"
                :key="section.title"
                v-show="selectedSection === index"
              )
                .layout-content
                  .layout-header
                    h3.layout-title {{ t(section.title) }}
                    .layout-line
                  p.layout-desc {{ t(section.desc) }}
                  .facilities
                    h4.facilities-title {{ t(planeData.layout.facilitiesTitle) }}
                    .facility-list
                      .facility-item(
                        v-for="facility in section.facilities"
                        :key="facility"
                      )
                        el-icon
                          i-ep-check
                        span {{ t(facility) }}

          //- Layout Overview Image (Vertical)
          .layout-image-wrapper(:data-aos="isMobile ? 'fade-up' : 'fade-left'" :data-aos-duration="isMobile ? 600 : 1000"
            :data-aos-delay="isMobile ? 0 : 200")
            .cabin-structure(ref="containerRef")
              //- Cabin Head
              .cabin-head
                el-image(
                  :src="planeData.layout.cabinHead"
                  fit="contain"
                  draggable="false"
                )

              //- Cabin Body (Scrollable)
              .cabin-body-container
                .cabin-body
                  .section-image(
                    v-for="(section, index) in planeData.layout.sections"
                    :key="index"
                    :class="{ 'active': selectedSection === index }"
                    :ref="(el) => { if (el) sectionRefs[index] = el }"
                    @click="handleSectionClick(index)"
                  )
                    el-image(
                      :src="section.image"
                      fit="contain"
                      draggable="false"
                    )

              //- Cabin Footer
              .cabin-footer
                el-image(
                  :src="planeData.layout.cabinFooter"
                  fit="contain"
                  draggable="false"
                )
</template>

<script setup lang="ts">
import { computed, onMounted, ref, onUnmounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import * as fleetConf from '../conf'
import AOS from 'aos'
import 'aos/dist/aos.css'
import { useI18n } from 'vue-i18n'
import type { Ref } from 'vue'
import { useDeviceType } from '@/composables/useDeviceType'

const { t } = useI18n()
const route = useRoute()

const planeData = computed(() => {
  const id = route.params.id as string
  return (fleetConf as any)[id] || null
})

const { isDesktop, isMobile } = useDeviceType()

const selectedSection = ref(0)
const sectionRefs: any[] = []  // 直接使用普通数组
const containerRef = ref<HTMLElement | null>(null)
const layoutSectionRef = ref<HTMLElement | null>(null)

// 强制重新计算布局的工具函数
const forceLayoutRecalculation = (reset = false) => {
  if (typeof window === 'undefined') return

  // 获取页面根元素
  const html = document.documentElement
  const body = document.body
  if (reset) {
    // 强制触发多种类型的reflow
    // html.offsetWidth
    body.offsetWidth
    // html.scrollWidth
    // body.scrollWidth
    // 短暂延迟后恢复（如果需要的话）
    setTimeout(() => {
      html.style.overflowX = ''
      body.style.overflowX = ''
    }, 500)
  } else {
    html.style.overflowX = 'hidden'
    body.style.overflowX = 'hidden'
  }
}
forceLayoutRecalculation()

onMounted(() => {
  if (typeof window !== 'undefined') {
    // Initialize AOS
    AOS.init({
      duration: 800,
      once: true,
      offset: 50
    })

    setTimeout(() => {
      forceLayoutRecalculation(true)
    }, 300)
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    AOS.refresh()
  }
})

// 处理点击客舱图片
const handleSectionClick = (index: number) => {
  selectedSection.value = index
}

// 检查并调整页面滚动位置
const checkAndAdjustScroll = () => {
  if (!layoutSectionRef.value || typeof window === 'undefined') return

  const layoutSection = layoutSectionRef.value
  const rect = layoutSection.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const sectionTop = scrollTop + rect.top

  // 如果页面滚动位置还没到达布局区域
  if (scrollTop < sectionTop) {
    // 将页面滚动到布局区域顶部
    layoutSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 处理导航按钮点击
const handleNavClick = (index: number) => {
  selectedSection.value = index
  checkAndAdjustScroll()
}

// 监听选中区域变化，滚动到对应位置
watch(selectedSection, (newIndex) => {
  nextTick(() => {
    const container = containerRef.value
    const targetElement = sectionRefs[newIndex]
    if (container && targetElement) {
      // 获取容器的可视区域高度
      const containerHeight = container.clientHeight
      // 获取目标元素相对于其父元素的偏移量
      const targetOffset = targetElement.offsetTop
      // 获取目标元素的高度
      const targetHeight = targetElement.offsetHeight

      // 计算滚动位置，使目标元素居中显示
      const scrollPosition = targetOffset - (containerHeight - targetHeight) / 2

      container.scrollTo({
        top: Math.max(0, scrollPosition),
        behavior: 'smooth'
      })
    }
  })
})

// 在组件卸载时清空引用数组
onBeforeUnmount(() => {
  sectionRefs.length = 0
})
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
