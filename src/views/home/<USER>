@use "sass:color";

// Variables
$primary-color: #D11242; // Brand's main red color

// Custom styles that complement Tailwind CSS
.home-page {
  section {
    padding: 5rem 5%;
    @media (max-width: 768px) {
      padding: 2rem 5%;
    }
  }

  // Hero section styles
  .hero-section {
    padding: 0;
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;

    @media (max-width: 768px) {
      height: 75vh;
    }

    @media (max-width: 480px) {
      height: 60vh; // 更小屏幕进一步降低高度
    }
  }

  // 轮播图样式
  .banner-carousel {
    width: 100%;
    height: 100%;

    :deep(.swiper-button-next),
    :deep(.swiper-button-prev) {
      width: 35px;
      height: 35px;
      background-color: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      transition: all 0.3s ease;
      @media (max-width: 768px) {
        display: none; // 移动端隐藏导航按钮
      }

      &:hover {
        background-color: white;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      &::after {
        font-size: 20px;
        color: #333;
      }

      &::after, &::before {
        font-size: 15px;
      }
    }

    :deep(.swiper-button-prev) {
      left: 20px;
    }

    :deep(.swiper-button-next) {
      right: 20px;
    }
  }

  .banner-content {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    }

    &.without-mask {
      &::before {
        display: none;
      }
    }

    .container {
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      z-index: 2;
      width: 100%;
      padding: 0 80px;
      //max-width: 1600px;
      margin: 0 auto;

      @media (max-width: 768px) {
        padding: 0 20px;
        align-items: flex-end;
        padding-bottom: 15vh;
        justify-content: center;
      }

      @media (max-width: 480px) {
        padding-bottom: 10vh;
      }
    }

    .hero-content {
      color: white;
      max-width: 800px;
      padding: 0;

      @media (max-width: 768px) {
        max-width: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .hero-title {
        font-size: 3.5rem;
        font-weight: bold;
        margin-bottom: 1.5rem;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 2.5rem;
          width: 100%;
        }

        @media (max-width: 480px) {
          font-size: 2rem;
          margin-bottom: 1rem;
        }
      }

      .hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        opacity: 0.9;

        @media (max-width: 768px) {
          font-size: 1.2rem;
          margin-bottom: 1.5rem;
          width: 100%;
        }

        @media (max-width: 480px) {
          font-size: 1rem;
          margin-bottom: 1.2rem;
        }
      }

      .primary-btn {
        display: inline-block;
        padding: 1rem 2.5rem;
        background-color: white;
        color: #1a365d;
        border-radius: 4px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid white;
        font-size: 1.125rem;
        cursor: pointer;

        &:hover {
          background-color: transparent;
          color: white;
        }

        @media (max-width: 768px) {
          padding: 0.8rem 2rem;
          font-size: 1rem;
          min-width: 160px;
          text-align: center;
        }

        @media (max-width: 480px) {
          padding: 0.7rem 1.8rem;
          font-size: 0.9rem;
          min-width: 140px;
        }
      }
    }
  }

  // Intro section styles
  .intro-section {
    background: linear-gradient(to bottom, #f8fafc, #ffffff);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 40%;
      height: 100%;
      background: linear-gradient(135deg, rgba($primary-color, 0.05), rgba($primary-color, 0.1));
      clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
      z-index: 1;
    }

    .intro-content {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 60px;
      align-items: center;

      @media (max-width: 1024px) {
        gap: 40px;
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
      }

      .intro-text {
        .section-title {
          font-size: 2.5rem;
          font-weight: bold;
          margin-bottom: 1.5rem;
          color: #666666;
          position: relative;
          display: inline-block;

          &::after {
            content: "";
            display: block;
            width: 100%;
            height: 4px;
            background: $primary-color;
            margin-top: 0.5rem;

            @media (max-width: 768px) {
              margin: 0.5rem auto 0;
              width: 80px;
            }
          }

          @media (max-width: 768px) {
            font-size: 2rem;
            width: 100%;
            text-align: center;
          }
        }

        .intro-description {
          font-size: 1.1rem;
          line-height: 1.8;
          color: #4a5568;
          margin-bottom: 2rem;
        }

        .learn-more-btn {
          display: inline-block;
          padding: 0.8rem 2rem;
          background-color: transparent;
          color: $primary-color;
          border: 2px solid $primary-color;
          border-radius: 4px;
          font-weight: 600;
          font-size: 1rem;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            background-color: $primary-color;
            color: white;
            transform: translateY(-2px);
          }

          @media (max-width: 768px) {
            display: block;
            width: fit-content;
            margin: 0 auto;
          }
        }
      }

      .intro-image {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        aspect-ratio: 4/3;
        background: #f1f5f9;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.6s ease;

          &:hover {
            transform: scale(1.05);
          }

        }

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 1px solid rgba($primary-color, 0.1);
          border-radius: 20px;
        }

        @media (max-width: 768px) {
          display: none;
        }
      }
    }
  }

  // Services section styles
  .services-section {
    background-color: #f7fafc;

    .section-title {
      text-align: center;
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 3rem;
      color: #666666;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;

      @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 640px) {
        grid-template-columns: 1fr;
      }
    }

    .service-card {
      background: white;
      padding: 2rem;
      border-radius: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-5px);
      }

      .service-icon {
        margin-bottom: 1.5rem;

        img {
          width: 64px;
          height: 64px;
        }
      }

      .service-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #2d3748;
      }

      .service-desc {
        color: #4a5568;
        line-height: 1.6;
      }
    }
  }

  // Core business section styles
  .core-business-section {
    background: #fff;
    position: relative;
    overflow: hidden;
    padding: 5rem 0;

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .section-title {
      text-align: center;
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 5rem;
      color: #666666;
      position: relative;

      &::after {
        content: "";
        display: block;
        width: 60px;
        height: 4px;
        background: $primary-color;
        margin: 1rem auto 0;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
        margin-bottom: 3rem;
      }
    }

    .business-list {
      position: relative;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      width: 100%;

      // 中间轴线
      &::before {
        content: "";
        position: absolute;
        left: 50%;
        top: 0;
        height: 100%;
        width: 2px;
        background: linear-gradient(to bottom,
          transparent,
          rgba($primary-color, 0.2) 10%,
          rgba($primary-color, 0.2) 90%,
          transparent
        );
        transform: translateX(-50%);

        @media (max-width: 991px) {
          left: 2rem;
        }
      }

      .business-item {
        display: flex;
        align-items: center;
        margin-bottom: 6rem;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        // 时间轴圆点
        &::before {
          content: "";
          position: absolute;
          left: 50%;
          width: 16px;
          height: 16px;
          background: #fff;
          border: 3px solid $primary-color;
          border-radius: 50%;
          transform: translateX(-50%);
          z-index: 2;

          @media (max-width: 991px) {
            left: 2rem;
          }
        }

        // 偶数项（右侧内容）
        &:nth-child(even) {
          .content-wrapper {
            margin-left: 52%;
          }

          .image-wrapper {
            left: 0;
            width: 45%;
          }
        }

        // 奇数项（左侧内容）
        &:nth-child(odd) {
          .content-wrapper {
            margin-right: 52%;
            text-align: right;

            .business-title::before {
              right: 0;
              left: auto;
            }

            .view-more-btn {
              flex-direction: row-reverse;
              margin-left: auto;

              .el-icon {
                margin-left: 0;
                margin-right: 0.5rem;
              }
            }
          }

          .image-wrapper {
            right: 0;
            width: 45%;
          }
        }

        @media (max-width: 991px) {
          flex-direction: column;
          margin-bottom: 4rem;

          &:nth-child(even),
          &:nth-child(odd) {
            .content-wrapper {
              width: calc(100% - 4rem);
              margin: 0 0 0 4rem;
              text-align: left;

              .business-title::before {
                left: 0;
                right: auto;
              }

              .view-more-btn {
                flex-direction: row;
                margin-left: 0;

                .el-icon {
                  margin-left: 0.5rem;
                  margin-right: 0;
                }
              }
            }

            .image-wrapper {
              position: relative;
              width: calc(100% - 4rem);
              margin: 0 0 1.5rem 4rem;
              left: auto;
              right: auto;
            }
          }
        }
      }

      .content-wrapper {
        width: 45%;
        background: #fff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        position: relative;
      }

      .business-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #666666;
        margin-bottom: 0.5rem;
        position: relative;
        padding-top: 1rem;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 30px;
          height: 3px;
          background: $primary-color;
        }
      }

      .business-desc {
        color: #666666;
        line-height: 1.8;
        margin: 1rem 0;
        font-size: 1rem;
      }

      .view-more-btn {
        padding: 0;
        color: color.adjust($primary-color, $lightness: -10%);
        font-size: 1rem;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        text-decoration: none;
        margin-top: 1rem;
        background: transparent;
        border: none;
        box-shadow: none;

        &:hover {
          transform: translateX(5px);
          background: transparent;
          color: color.adjust($primary-color, $lightness: -10%);
        }

        &:focus {
          background: transparent;
          color: $primary-color;
        }

        .el-icon {
          margin-left: 0.5rem;
        }
      }

      .image-wrapper {
        position: absolute;
        height: 240px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        background: #fff;
        padding: 8px;

        .business-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }

        @media (max-width: 991px) {
          height: 200px;
        }
      }
    }

    .more-services-container {
      margin-top: 5rem;
      width: 100%;
      display: flex;
      justify-content: center;

      .more-services-btn {
        min-width: 200px;
        height: 50px;
        font-size: 1.1rem;
        background-color: $primary-color;
        border-color: $primary-color;
        transition: all 0.3s ease;
        border-radius: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        }

        .el-icon {
          transition: transform 0.3s ease;
        }

        &:hover .el-icon {
          transform: translateX(5px);
        }

        @media (max-width: 768px) {
          min-width: 180px;
          height: 44px;
          font-size: 1rem;
        }
      }
    }

    @media (max-width: 768px) {
      padding: 2rem 0;
    }
  }

  @keyframes morphing {
    0% {
      border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
      border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    }
    50% {
      border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    }
    75% {
      border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    }
    100% {
      border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
  }

  // Advantages section styles
  .advantages-section {
    background-color: #ffffff;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba($primary-color, 0.03) 0%, rgba($primary-color, 0.08) 100%);
      clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    }

    .section-title {
      text-align: center;
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 4rem;
      color: #666666;
      position: relative;

      &::after {
        content: "";
        display: block;
        width: 60px;
        height: 4px;
        background: $primary-color;
        margin: 1rem auto 0;
      }

      @media (max-width: 768px) {
        font-size: 1.8rem;
        margin-bottom: 3rem;
      }
    }

    .advantages-grid {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      max-width: 1200px;
      margin: 0 auto;

      @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
      }

      @media (max-width: 640px) {
        grid-template-columns: 1fr;
        gap: 25px;
      }

      .advantage-card {
        background: white;
        padding: 3rem 2rem;
        border-radius: 20px;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba($primary-color, 0.1);
        cursor: pointer;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, rgba($primary-color, 0.05), transparent);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

          &::before {
            opacity: 1;
          }

          .advantage-icon-wrapper {
            transform: scale(1.1);

            .advantage-icon {
              animation: pulse 1.5s infinite;
            }
          }

          .advantage-number {
            opacity: 0.15;
            transform: translate(-10px, -10px) scale(1.1);
          }
        }

        .advantage-icon-wrapper {
          position: relative;
          width: 80px;
          height: 80px;
          margin-bottom: 2rem;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          background: linear-gradient(135deg, rgba($primary-color, 0.08), rgba($primary-color, 0.03));

          .advantage-icon {
            font-size: 2.5rem;
            transition: transform 0.3s ease;
            color: $primary-color;
            opacity: 0.9;

            // 移除彩色样式
            &.globe,
            &.shield-alt,
            &.headset {
              color: $primary-color;
            }
          }
        }

        .advantage-content {
          position: relative;
          z-index: 2;
        }

        .advantage-title {
          font-size: 1.4rem;
          font-weight: 600;
          margin-bottom: 1rem;
          color: #1a365d;
          position: relative;
        }

        .advantage-desc {
          color: #4a5568;
          line-height: 1.7;
          font-size: 1.05rem;
        }

        .advantage-number {
          position: absolute;
          right: -14px;
          bottom: -20px;
          font-size: 8rem;
          font-weight: 800;
          color: rgba($primary-color, 0.08);
          transition: all 0.4s ease;
          line-height: 1;
          z-index: 1;
          font-family: "Inter", "Noto Sans SC", serif;
        }
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }
  }

  // Custom styles for service cards
  .service-card {
    .service-icon {
      img {
        filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      }
    }

    &:hover {
      .service-icon img {
        transform: scale(1.05);
        transition: transform 0.3s ease;
      }
    }
  }

  // Custom styles for advantage cards
  .advantage-card {
    .advantage-icon {
      img {
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      .advantage-icon img {
        transform: rotate(5deg);
      }
    }
  }

  // AOS custom animations
  [data-aos] {
    &.fade-up {
      transform: translateY(30px);
      opacity: 0;
      transition-property: transform, opacity;

      &.aos-animate {
        transform: translateY(0);
        opacity: 1;
      }
    }

    &.fade-right {
      transform: translateX(-30px);
      opacity: 0;
      transition-property: transform, opacity;

      &.aos-animate {
        transform: translateX(0);
        opacity: 1;
      }
    }

    &.fade-left {
      transform: translateX(30px);
      opacity: 0;
      transition-property: transform, opacity;

      &.aos-animate {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }

  // Contact section styles
  .contact-section {
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    overflow: hidden;

    &::before,
    &::after {
      content: "";
      position: absolute;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($primary-color, 0.02) 100%);
      z-index: 1;
    }

    &::before {
      top: -100px;
      left: -100px;
    }

    &::after {
      bottom: -100px;
      right: -100px;
    }

    .contact-content {
      position: relative;
      z-index: 2;
      text-align: center;
      max-width: 800px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .contact-title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #1a365d;
      margin-bottom: 1.5rem;
      position: relative;
      display: inline-block;

      &::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background: $primary-color;
        border-radius: 2px;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .contact-subtitle {
      font-size: 1.5rem;
      color: #4a5568;
      margin-bottom: 3rem;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1.25rem;
        margin-bottom: 2rem;
      }
    }

    .contact-btn {
      padding: 1rem 3rem;
      font-size: 1.1rem;
      border-radius: 50px;
      background: $primary-color;
      border-color: $primary-color;
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba($primary-color, 0.2);

        &::before {
          width: 300px;
          height: 300px;
        }

        .el-icon {
          transform: translateX(5px);
        }
      }

      .el-icon {
        transition: transform 0.3s ease;
        font-size: 1.2em;
        vertical-align: middle;
      }

      @media (max-width: 768px) {
        padding: 0.8rem 2.5rem;
        font-size: 1rem;
      }
    }
  }
}

// Dark mode overrides if needed
:root[class~="dark"] {
  .home-page {
    .hero-section {
      &::before {
        opacity: 0.25;
      }
    }

    .service-card {
      background: rgba(255, 255, 255, 0.05);
    }

    .advantage-card {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
    }
  }
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  touch-action: none;
}

