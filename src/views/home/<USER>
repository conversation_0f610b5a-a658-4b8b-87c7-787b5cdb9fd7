<template lang="pug">
.home-page
  // Banner部分
  section.hero-section
    swiper.banner-carousel(
      :modules="swiperModules"
      :slides-per-view="1"
      :loop="true"
      :effect="'slide'"
      :autoplay="{delay: 3000, disableOnInteraction: false}"
      :navigation="true"
    )
      swiper-slide(v-for="item in bannerItems" :key="item.id")
        .banner-content(:class="item.className" :style="{ backgroundImage: `url(${item.image})` }")
          .container
            .hero-content(data-aos="fade-up")
              h1.hero-title {{ t(item.title) }}
              p.hero-subtitle {{ t(item.subtitle) }}
              button.primary-btn(@click="handleBannerBtn(item)") {{ t(item.buttonText) }}

  // 公司简介
  section.intro-section.services-section#home-intro
    .container.mx-auto.px-4
      .intro-content
        .intro-text(data-aos="fade-right")
          h2.section-title {{ t('app.home.intro.title') }}
          p.intro-description {{ t('app.home.intro.desc') }}
          button.learn-more-btn(@click="router.push({name: 'about'})") {{ t('app.service.viewMore') }}
        .intro-image(data-aos="fade-left")
          //- Temporarily commented until image is added
          img(:src="aboutImg" alt="Private Jet")

  // 核心业务
  section.core-business-section
    .container.mx-auto.px-4
      h2.section-title(data-aos="fade-up") {{ t('app.home.services.title') }}
      .business-list
        .business-item(
          v-for="(service, index) in services"
          :key="service.id"
          data-aos="fade-up"
          data-aos-delay="200"
        )
          .content-wrapper
            h3.business-title {{ t(service.title) }}
            p.business-desc {{ t(service.description) }}
            el-button.view-more-btn(
              type="default"
              @click="router.push({name: 'serviceDetail', params: {id: service.routeId}})"
            )
              span {{ t('app.service.viewMore') }}
              el-icon.ml-2
                ArrowRight
          .image-wrapper
            img.business-image(:src="getServiceImage(index + 1)" :alt="t(service.title)")
      .more-services-container(data-aos="fade-up")
        el-button.more-services-btn(
          type="primary"
          size="large"
          @click="router.push({name: 'service'})"
        )
          span {{ t('app.home.moreServices') }}
          el-icon.ml-2
            ArrowRight

  // 公司优势
  section.advantages-section
    .container.mx-auto.px-4
      h2.section-title(data-aos="fade-up") {{ t('app.home.advantages.title') }}
      .advantages-grid
        .advantage-card(
          v-for="(advantage, index) in advantages"
          :key="advantage.id"
          data-aos="zoom-in"
          data-aos-delay="200"
          @click="router.push({name: 'fleet'})"
        )
          .advantage-icon-wrapper
            .advantage-icon(:class="advantage.icon")
              font-awesome-icon(:icon="['fas', advantage.icon]")
          .advantage-content
            h3.advantage-title {{ t(advantage.title) }}
            p.advantage-desc {{ t(advantage.description) }}
          .advantage-number {{ index + 1 }}

  // 联系我们
  section.contact-section
    .container.mx-auto.px-4
      .contact-content(data-aos="fade-up")
        h2.contact-title {{t('app.home.contact.title') }}
        p.contact-subtitle {{t('app.home.contact.desc') }}
        el-button.contact-btn(
          type="primary"
          size="large"
          @click="router.push({name: 'contact'})"
        )
          span {{t('app.home.contact.btn') }}

  StructuredData(type="breadcrumb" :data="{breadcrumb: route.meta.breadcrumb,baseUrl: 'https://www.panamericanjet.com'}")
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {useI18n} from 'vue-i18n'
import AOS from 'aos'
// import {useHeaderScroll} from '@/composables/useHeaderScroll'
// 导入banner图片
import banner1 from '@/assets/images/banner/webp/home-banner1.webp'
import banner2 from '@/assets/images/banner/webp/home-banner2.webp'
import banner3 from '@/assets/images/banner/webp/home-banner3.webp'
import {useRoute, useRouter} from 'vue-router'
import StructuredData from '@/components/StructuredData.vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Navigation, EffectFade } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-fade'
import { ArrowRight } from '@element-plus/icons-vue'

import aboutImg from '@/assets/images/home/<USER>/about.webp'
// 导入服务相关图片
import service1 from '@/assets/images/service/webp/service0.webp'
import service2 from '@/assets/images/service/webp/service1.webp'
import service3 from '@/assets/images/service/webp/service3.webp'

const router = useRouter()
const route = useRoute()
const {t} = useI18n()

// 获取服务图片的辅助函数
const getServiceImage = (index: number) => {
  const images = {
    1: service1,
    2: service2,
    3: service3
  }
  return images[index as keyof typeof images]
}

// Banner数据
const bannerItems = ref([
  {
    id: 1,
    image: banner1,
    title: 'app.home.banner.b1Title',
    subtitle: 'app.home.banner.b1Subtitle',
    buttonText: 'app.home.banner.b1Btn',
    routeName: 'contact'
  },
  {
    id: 2,
    image: banner2,
    title: 'app.home.banner.b2Title',
    subtitle: 'app.home.banner.b2Subtitle',
    buttonText: 'app.home.banner.b2Btn',
    routeName: 'fleet'
  },
  {
    id: 3,
    image: banner3,
    title: 'app.home.banner.b3Title',
    subtitle: 'app.home.banner.b3Subtitle',
    buttonText: 'app.home.banner.b3Btn',
    routeName: 'contact',
    className: 'without-mask'
  }
])

// 核心业务数据
const services = ref([
  {
    id: 1,
    title: 'app.home.services.s1Title',
    description: 'app.home.services.s1Desc',
    routeId: "lease"
  },
  {
    id: 2,
    title: 'app.home.services.s2Title',
    description: 'app.home.services.s2Desc',
    routeId: "charter"
  },
  {
    id: 3,
    title: 'app.home.services.s3Title',
    description: 'app.home.services.s3Desc',
    routeId: "asset"
  }
])

// 公司优势数据
const advantages = ref([
  {
    id: 1,
    icon: 'shield-alt',
    title: 'app.home.advantages.a1Title',
    description: 'app.home.advantages.a1Desc'
  },
  {
    id: 2,
    icon: 'headset',
    title: 'app.home.advantages.a2Title',
    description: 'app.home.advantages.a2Desc'
  },
  {
    id: 3,
    icon: 'globe',
    title: 'app.home.advantages.a3Title',
    description: 'app.home.advantages.a3Desc'
  }
])
// Use the composable to handle header scroll effect
// useHeaderScroll('#home-intro')
onMounted(() => {
  AOS.init({
    duration: 1000,
    easing: 'ease-out',
    once: true
  })
})

function handleBannerBtn (item: any) {
  router.push({name: item.routeName})
}

const swiperModules = [Autoplay, Navigation, EffectFade]
</script>

<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
