// Import some shared variables or mixins if you have them
// @use '@/styles/variables' as *;
@use "sass:color";

$primary-color: #D11242;
$text-primary: #1a202c;
$text-secondary: #4a5568;
$text-light: #718096;

@mixin heading-base {
  color: $text-primary;
  line-height: 1.3;
  margin-bottom: 1rem;
}

@mixin body-text {
  color: $text-secondary;
  line-height: 1.8;
  font-size: 1.1rem;
}

@keyframes kenburns {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

// Add new animations
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

// Add new animation keyframes
@keyframes morphing {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: rotate(-15deg) scale(1);
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    transform: rotate(-10deg) scale(1.1);
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: rotate(-15deg) scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

// Add new keyframes for diamond animation
@keyframes diamondPulse {
  0%, 100% {
    transform: translateX(-50%) rotate(45deg) scale(1);
    background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba($primary-color, 0.05));
  }
  50% {
    transform: translateX(-50%) rotate(45deg) scale(1.2);
    background: linear-gradient(135deg, rgba($primary-color, 0.2), rgba($primary-color, 0.1));
  }
}

@keyframes diamondSpin {
  0% {
    transform: translateX(-50%) rotate(45deg);
  }
  100% {
    transform: translateX(-50%) rotate(405deg);
  }
}

@keyframes diamondGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0.4);
  }
  50% {
    box-shadow: 0 0 10px 2px rgba($primary-color, 0.2);
  }
}

@keyframes diamond3DRotate {
  0% {
    transform: translateX(-50%) rotateX(0deg) rotateY(0deg) rotateZ(45deg);
  }
  25% {
    transform: translateX(-50%) rotateX(90deg) rotateY(45deg) rotateZ(45deg);
  }
  50% {
    transform: translateX(-50%) rotateX(180deg) rotateY(90deg) rotateZ(45deg);
  }
  75% {
    transform: translateX(-50%) rotateX(270deg) rotateY(45deg) rotateZ(45deg);
  }
  100% {
    transform: translateX(-50%) rotateX(360deg) rotateY(0deg) rotateZ(45deg);
  }
}

@keyframes diamondShine {
  0%, 100% {
    background: linear-gradient(135deg,
      rgba($primary-color, 0.2) 0%,
      rgba($primary-color, 0.1) 50%,
      rgba($primary-color, 0.2) 100%
    );
    box-shadow: 0 0 15px rgba($primary-color, 0.2),
    inset 0 0 4px rgba($primary-color, 0.3);
  }
  50% {
    background: linear-gradient(135deg,
      rgba($primary-color, 0.3) 0%,
      rgba($primary-color, 0.15) 50%,
      rgba($primary-color, 0.3) 100%
    );
    box-shadow: 0 0 20px rgba($primary-color, 0.3),
    inset 0 0 8px rgba($primary-color, 0.4);
  }
}

@keyframes planeFly {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg) scale(1);
  }
  25% {
    transform: translateX(-50%) translateY(-60%) rotate(-5deg) scale(1.1);
  }
  75% {
    transform: translateX(-50%) translateY(-40%) rotate(5deg) scale(0.95);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg) scale(1);
  }
}

@keyframes planeHover {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-50%) translateY(-50%) rotate(360deg);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(720deg);
  }
}

@keyframes planeGlow {
  0%, 100% {
    filter: drop-shadow(0 0 3px rgba($primary-color, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba($primary-color, 0.5));
  }
}

// Add new keyframes for plane animation
@keyframes planeFloat {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  25% {
    transform: translateX(-50%) translateY(-70%) rotate(-5deg);
  }
  75% {
    transform: translateX(-50%) translateY(-30%) rotate(5deg);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
}

@keyframes planeFloatRight {
  0% {
    transform: translateX(50%) translateY(-50%) rotate(180deg);
  }
  25% {
    transform: translateX(50%) translateY(-70%) rotate(175deg);
  }
  75% {
    transform: translateX(50%) translateY(-30%) rotate(185deg);
  }
  100% {
    transform: translateX(50%) translateY(-50%) rotate(180deg);
  }
}

// General page styling
.about-page {
  @include body-text;
  background-color: #f8f9fa; // A light, clean background
  width: 100%;
  overflow-x: hidden; // 防止水平溢出
  position: relative;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(-45deg, rgba(209, 18, 66, 0.03), rgba(209, 18, 66, 0.01), rgba(255, 255, 255, 0.02), rgba(209, 18, 66, 0.02));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    z-index: -1;
  }

  h1 {
    @include heading-base;
    font-size: 3.5rem;
    font-weight: 700;
    letter-spacing: -0.02em;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  h2 {
    @include heading-base;
    font-size: 2.8rem;
    font-weight: 600;
    letter-spacing: -0.01em;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  h3 {
    @include heading-base;
    font-size: 2rem;
    font-weight: 600;

    @media (max-width: 768px) {
      font-size: 1.75rem;
    }
  }

  h4 {
    @include heading-base;
    font-size: 1.5rem;
    font-weight: 600;

    @media (max-width: 768px) {
      font-size: 1.25rem;
    }
  }

  p {
    margin-bottom: 1.5rem;
  }

  .section-title {
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 3rem;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: $primary-color;
    }
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    width: 100%;
    box-sizing: border-box; // 确保padding不会导致溢出

    @media (max-width: 768px) {
      padding: 0 10px; // 移动端减少padding
    }
  }

  .content-section {
    padding: 120px 0;
    width: 100%;
    box-sizing: border-box;
  }


  // 1. Hero Section
  .hero-section {
    width: 100%;
    height: 100vh;
    position: relative;
    color: #fff;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
    overflow: hidden;

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("@/assets/images/about/webp/banner.webp");
      background-size: cover;
      background-position: center;
      animation: kenburns 20s ease-out infinite alternate;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.1); // Dark overlay for text contrast
    }

    .hero-content {
      position: relative;
      z-index: 1;
      padding: 30vh 8% 0;

      h1 {
        color: #fff;
        text-shadow: 2px 2px 10px rgba(0, 0, 0, 0.7);
        margin-bottom: 1.5rem;
      }

      p {
        font-size: 1.4rem;
        font-weight: 300;
        line-height: 1.6;
        max-width: 600px;
        text-shadow: 1px 1px 10px rgba(0, 0, 0, 0.7);

        @media (max-width: 768px) {
          font-size: 1.2rem;
        }
      }
    }

    @media (max-width: 768px) {
      height: 75vh;
    }

    @media (max-width: 480px) {
      height: 60vh; // 更小屏幕进一步降低高度
    }
  }

  // 2. Intro Section
  .intro-section {
    background-color: #fff;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: -30%;
      right: -20%;
      width: 80%;
      height: 160%;
      background: linear-gradient(
          135deg,
          rgba($primary-color, 0.05) 0%,
          rgba($primary-color, 0.08) 25%,
          rgba($primary-color, 0.12) 50%,
          rgba($primary-color, 0.08) 75%,
          rgba($primary-color, 0.05) 100%
      );
      animation: morphing 30s cubic-bezier(0.4, 0, 0.2, 1) infinite,
      gradient 20s cubic-bezier(0.4, 0, 0.2, 1) infinite;
      background-size: 200% 200%;
      z-index: 0;

      @media (max-width: 768px) {
        display: none;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 20%;
      left: -10%;
      width: 60%;
      height: 120%;
      background: linear-gradient(
          -45deg,
          rgba($primary-color, 0.03) 0%,
          rgba($primary-color, 0.06) 50%,
          rgba($primary-color, 0.03) 100%
      );
      border-radius: 40% 60% 60% 40% / 70% 30% 70% 30%;
      animation: morphing 25s cubic-bezier(0.4, 0, 0.2, 1) infinite reverse;
      z-index: 0;

      @media (max-width: 768px) {
        display: none;
      }
    }

    @media (max-width: 768px) {
      background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1) 0%,
          rgba(255, 255, 255, 0.95) 15%,
          rgba(255, 255, 255, 0.9) 100%
      );

      &::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            135deg,
            rgba($primary-color, 0.02) 0%,
            rgba($primary-color, 0.01) 25%,
            rgba(255, 255, 255, 0.01) 50%,
            rgba($primary-color, 0.01) 75%,
            rgba($primary-color, 0.02) 100%
        );
        background-size: 400% 400%;
        animation: gradient 15s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        z-index: -1;
        pointer-events: none;
      }
    }

    .intro-content {
      position: relative;
      z-index: 1;
    }

    .intro-main {
      margin-bottom: 2rem;

      .intro-header {
        display: flex;
        gap: 4rem;
        align-items: center;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 0;
          margin: 0 -15px;
          background: rgba(255, 255, 255, 0.7);
          backdrop-filter: blur(10px);
          border-radius: 20px;
          padding: 1rem;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        }

        .intro-text-wrapper {
          flex: 1;
          position: relative;
          z-index: 2;
          background: rgba(255, 255, 255, 0.9);
          padding: 2.5rem;
          border-radius: 0 20px 20px 0;
          position: relative;
          margin-left: -2.5rem;
          box-shadow: 10px 0 30px rgba(0, 0, 0, 0.05);

          @media (max-width: 768px) {
            margin-left: 0;
            border-radius: 0 0 20px 20px;
            padding: 1.5rem;
            margin: -30px 15px 0;
            background: #fff;
            order: 2;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
            position: relative;
          }

          .text-container {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 2rem;

            @media (max-width: 768px) {
              padding-left: 0;
              padding-top: 1rem;
              text-align: center;
            }

            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              width: 4px;
              height: 100%;
              background: linear-gradient(180deg,
                $primary-color,
                $primary-color 30%,
                rgba($primary-color, 0.1) 90%,
                transparent
              );
              opacity: 0.85;
              border-radius: 2px;

              @media (max-width: 768px) {
                left: 50%;
                transform: translateX(-50%);
                top: -20px;
                width: 60px;
                height: 4px;
                background: linear-gradient(90deg,
                  transparent,
                  $primary-color 20%,
                  $primary-color 80%,
                  transparent
                );
              }
            }

            .intro-text {
              @media (max-width: 768px) {
                text-align: center;
                margin: 0 auto;
                max-width: 90%;
              }

              &.highlight {
                @media (max-width: 768px) {
                  text-align: center;
                  margin: 0 auto 1rem;
                }
              }
            }
          }
        }

        .main-image {
          flex: 1.2;
          position: relative;
          height: 400px;
          border-radius: 20px;
          overflow: hidden;
          isolation: isolate;
          cursor: pointer;

          @media (max-width: 768px) {
            width: 100%;
            height: 200px;
            border-radius: 20px 20px 0 0;
            margin: 0 15px;
            order: 1;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.08);
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
          }

          &::after {
            content: "";
            position: absolute;
            inset: 0;
            background: linear-gradient(105deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0) 40%,
              rgba(255, 255, 255, 0.15) 45%,
              rgba(255, 255, 255, 0.2) 47%,
              rgba(255, 255, 255, 0.15) 49%,
              rgba(255, 255, 255, 0) 54%,
              rgba(255, 255, 255, 0) 100%
            );
            background-size: 200% 100%;
            background-position: 100% 0;
            pointer-events: none;
            z-index: 1;
            transition: background-position 1.2s cubic-bezier(0.165, 0.84, 0.44, 1);

            @media (max-width: 768px) {
              display: none;
            }
          }

          &:hover {
            &::after {
              background-position: -100% 0;
            }

            img {
              transform: scale(1.03);
              @media (max-width: 768px) {
                transform: none;
              }
            }
          }
        }
      }
    }

    .intro-gallery {
      position: relative;
      padding-bottom: 2rem;

      .gallery-title {
        font-size: 2rem;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 1.5rem;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          bottom: -0.5rem;
          left: 0;
          width: 40px;
          height: 3px;
          background-color: $primary-color;
        }

        @media (max-width: 768px) {
          font-size: 1.6rem;
          margin-bottom: 1.5rem;
        }
      }

      .office-swiper {
        padding: 1rem;

        .swiper-wrapper {
          padding: 0.5rem 0;
        }

        :deep(.swiper-pagination) {
          bottom: -10px;

          .swiper-pagination-bullet {
            background: $primary-color;
            opacity: 0.5;
            transition: all 0.3s ease;

            &-active {
              opacity: 1;
              transform: scale(1.2);
            }
          }
        }

        :deep(.swiper-button-next),
        :deep(.swiper-button-prev) {
          color: $primary-color;
          opacity: 0;
          transition: all 0.3s ease;

          &::after {
            font-size: 1.5rem;
          }
        }

        &:hover {
          :deep(.swiper-button-next),
          :deep(.swiper-button-prev) {
            opacity: 1;
          }
        }

        .gallery-item {
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba($primary-color, 0.05), transparent);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            transform: translateY(-5px);

            &::after {
              opacity: 1;
            }
          }

          img {
            display: block;
            width: 100%;
            height: 250px;
            object-fit: cover;
          }
        }
      }
    }
  }

  // 3. Team Section
  .team-section {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
    padding: 40px 0;

    &::before {
      content: "";
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle at center, rgba($primary-color, 0.03) 0%, transparent 70%);
      animation: gradient 20s ease infinite;
      z-index: 0;
    }

    .container {
      position: relative;
      z-index: 1;
    }

    // CEO Card Styles
    .ceo-card {
      max-width: 1200px;
      margin: 3rem auto 5rem;
      border-radius: 24px;
      overflow: hidden;
      transition: transform 0.4s ease, box-shadow 0.4s ease;
      position: relative;
      z-index: 2;

      &::before, &::after {
        content: "";
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba($primary-color, 0.03), rgba($primary-color, 0.08));
        filter: blur(40px);
        z-index: -1;
      }

      &::before {
        width: 300px;
        height: 300px;
        top: -100px;
        right: -100px;
      }

      &::after {
        width: 200px;
        height: 200px;
        bottom: -50px;
        left: 30%;
      }

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 40px 80px rgba(0, 0, 0, 0.12);

        .ceo-avatar {
          transform: scale(1.05);
        }

        @media (max-width: 768px) {
          transform: none;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.06);

          .ceo-avatar {
            transform: none;
          }
        }
      }

      &.reverse-layout {
        &::before {
          right: auto;
          left: -100px;
        }

        .ceo-content {
          flex-direction: row-reverse;

          .ceo-image-wrapper {
            margin-left: 0;
            margin-right: 18px;
          }

          .ceo-name, .ceo-career {
            text-align: right;
          }

          .ceo-name {
            &::after {
              left: auto;
              right: 0;
              background: linear-gradient(to left, #D11242, rgba(254, 254, 254, 0) 80%);
            }
          }

          .ceo-details {
            .ceo-intro {
              border-left: none;
              border-right: 3px solid rgba($primary-color, 0.2);
              background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.7) 100%
              );
              border-radius: 0 12px 12px 12px;
            }
          }
        }
      }

      .ceo-content {
        display: flex;
        gap: 3rem;
        position: relative;
        align-items: center;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 85%;
          height: 1px;
          background: linear-gradient(90deg,
            transparent,
            rgba($primary-color, 0.1) 20%,
            rgba($primary-color, 0.1) 80%,
            transparent
          );
        }

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 1.5rem;
          padding: 2rem 1rem;
          text-align: center;

          &::before {
            display: none;
          }
        }
      }

      .ceo-image-wrapper {
        flex: 0 0 300px;
        min-width: 280px;
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        line-height: 0;
        font-size: 0;
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        margin-left: 18px;

        &::after {
          content: "";
          position: absolute;
          inset: 0;
          background: linear-gradient(
              105deg,
              transparent 0%,
              transparent 40%,
              rgba(255, 255, 255, 0.1) 45%,
              rgba(255, 255, 255, 0.15) 47%,
              rgba(255, 255, 255, 0.1) 49%,
              transparent 54%,
              transparent 100%
          );
          background-size: 200% 100%;
          background-position: 100% 0;
          transition: background-position 1.2s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        &:hover {
          &::after {
            background-position: -100% 0;
          }
        }

        @media (max-width: 768px) {
          flex: 0 0 auto;
          min-width: auto;
          max-width: 280px;
          width: 85%;
          margin: 0 auto;
          border-width: 3px;
          border-radius: 16px;
          box-shadow: 0 15px 30px rgba($primary-color, 0.12);
          transform: none;

          &::after {
            display: none;
          }
        }
      }

      .ceo-avatar {
        width: 100%;
        height: 400px;
        object-fit: cover;
        display: block;
        transition: transform 0.6s ease;

        @media (max-width: 768px) {
          height: 380px;
        }
      }

      .ceo-details {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        padding: 2rem 0 1rem;
        position: relative;

        .ceo-name {
          font-size: 2.2rem;
          word-wrap: break-word;
          font-weight: 700;
          margin: 0 0 0.8rem;
          color: $text-primary;
          letter-spacing: -0.02em;
          line-height: 1.2;
          position: relative;
          display: inline-block;
          background: linear-gradient(135deg,
            $text-primary 0%,
            color.adjust($text-primary, $lightness: 15%) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          &::after {
            content: "";
            position: absolute;
            bottom: -0.8rem;
            left: 0;
            width: 80%;
            height: 0.2rem;
            background: linear-gradient(to right, #D11242, rgba(254, 254, 254, 0) 80%);
            opacity: 0.3;
          }

          @media (max-width: 768px) {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 0.3rem;
            width: 100%;

            &::after {
              left: 50%;
              transform: translateX(-50%);
              width: 2.5rem;
              height: 0.25rem;
              bottom: -0.3rem;
            }
          }
        }

        .ceo-career {
          font-size: 1.6rem;
          color: $primary-color;
          margin: 0.3rem 0 0.5rem;
          font-weight: 600;
          letter-spacing: 0.01em;
          word-wrap: break-word;
          text-transform: uppercase;
          background: linear-gradient(135deg, $primary-color, rgba($primary-color, 0.7));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          position: relative;

          &::before {
            content: "✦";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            color: $primary-color;
            font-size: 1rem;
            opacity: 0.7;
          }

          @media (max-width: 768px) {
            font-size: 1.2rem;
            text-align: center;
            margin: 0.3rem 0 0.5rem;
            letter-spacing: 0.05em;
            padding-left: 0;

            &::before {
              display: none;
            }
          }
        }

        .ceo-intro {
          font-size: 1.1rem;
          line-height: 1.8;
          color: $text-secondary;
          margin: 0 0 1.5rem;
          position: relative;
          border-left: 3px solid rgba($primary-color, 0.2);
          word-wrap: break-word;
          background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.7) 0%,
            rgba(255, 255, 255, 0.2) 100%
          );
          padding: 1.5rem;
          border-radius: 12px 0 12px 12px;
          font-weight: 400;
          letter-spacing: 0.01em;
          text-align: justify;

          p {
            margin: 0;

            & + p {
              margin-top: 1rem;
            }
          }

          @media (max-width: 768px) {
            text-align: center;
            padding: 1.2rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            font-size: 1rem;
            line-height: 1.6;
            margin: 0;
            border-left: none;
            position: relative;
            margin-top: 0.5rem;
          }
        }
      }

    }

    // Team Grid Styles
    .team-grid {
      display: flex;
      flex-direction: column;
      gap: 6rem;
      margin-top: 4rem;
      position: relative;
      z-index: 1;

      @media (max-width: 768px) {
        gap: 3.5rem;
      }
    }

    .team-member {
      display: flex;
      align-items: center;
      gap: 4rem;
      position: relative;

      &.reverse-layout {
        flex-direction: row-reverse;

        .member-image-section {
          .image-overlay {
            clip-path: polygon(100% 0, 100% 100%, 0 100%, 20% 50%, 0 0);
          }
        }

        .member-content-section {
          .content-wrapper {
            margin-right: auto;
            margin-left: 0;

            &::before {
              left: auto;
              right: -2rem;
            }
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column !important;
        gap: 0;
        max-width: 600px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 30px;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
        padding: 2rem 1.5rem;

        &.reverse-layout {
          .member-content-section {
            .content-wrapper {
              margin: 0;
            }
          }
        }

        .member-content-section {
          .content-wrapper {
            .member-info {
              background: rgba(255, 255, 255, 0.95);
              padding: 1.5rem;
              border-radius: 15px;
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
              margin-bottom: 1rem;

              .title-area {
                text-align: center;
                margin-bottom: 0.8rem;

                .member-name {
                  font-size: 1.6rem;
                  margin-bottom: 0.3rem;

                  &::after {
                    left: 50%;
                    transform: translateX(-50%);
                    width: 2rem;
                    height: 0.15rem;
                    bottom: 0;
                  }
                }

                .member-career {
                  font-size: 1rem;
                  letter-spacing: 0.03em;
                }
              }

              .member-extra {
                text-align: center;
                margin-bottom: 0.8rem;
                font-size: 0.95rem;
                line-height: 1.5;
                letter-spacing: 0.01em;
                padding: 0 0.5rem;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: -0.4rem;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 40px;
                  height: 1px;
                  background: rgba($primary-color, 0.2);
                }
              }
            }

            .member-intro-wrapper {
              .member-intro {
                text-align: center;
                background: rgba(255, 255, 255, 0.9);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
                padding: 1.2rem;
                font-size: 0.95rem;
                line-height: 1.6;
                margin-top: 0.5rem;

                &::before {
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 3rem;
                  top: -0.8rem;
                }
              }
            }
          }
        }
      }

      .member-image-section {
        flex: 0 0 400px;
        position: relative;
        z-index: 2;

        @media (max-width: 768px) {
          flex: 0 0 auto;
          width: 100%;
          margin-top: -4rem;
          margin-bottom: -1rem;
          padding: 0 1rem;

          .member-image-container {
            height: 350px;
            transform: none !important;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);

            img {
              display: block;
              filter: brightness(1.02);
            }
          }
        }

        .member-image-container {
          width: 100%;
          position: relative;
          overflow: hidden;
          border-radius: 30px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
          transition: all 0.5s ease;

          @media (max-width: 768px) {
            height: 350px;
            transform: none !important;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
          }

          img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
          }

          .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(
                to top,
                rgba(0, 0, 0, 0.8) 0%,
                rgba(0, 0, 0, 0.4) 50%,
                transparent 100%
            );
            padding: 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            clip-path: polygon(0 0, 100% 0, 80% 50%, 100% 100%, 0 100%);

            @media (max-width: 768px) {
              display: none;
            }

            .overlay-content {
              color: #fff;
              text-align: left;

              .member-name {
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
                font-weight: 600;
              }

              .member-career {
                font-size: 1.1rem;
                opacity: 0.9;
              }
            }
          }

          &:hover {
            transform: perspective(1000px) rotateY(0deg);

            .image-overlay {
              opacity: 1;
              transform: translateY(0);
            }

            img {
              transform: scale(1.1);
            }

            @media (max-width: 1024px) {
              transform: none !important;

              img {
                transform: none;
              }
            }
          }
        }
      }

      .member-content-section {
        flex: 1;
        position: relative;

        .content-wrapper {
          max-width: 600px;
          margin-left: auto;
          padding: 2rem;
          position: relative;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          border-radius: 20px;
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);

          &::before {
            content: "";
            position: absolute;
            left: -2rem;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom,
              $primary-color,
              rgba($primary-color, 0.2)
            );
          }

          @media (max-width: 1024px) {
            margin: 0;
            padding: 0;
            background: none;
            backdrop-filter: none;
            box-shadow: none;

            &::before {
              display: none;
            }
          }

          .member-info {
            .title-area {
              margin-bottom: 1.5rem;

              @media (max-width: 768px) {
                text-align: center;
                margin-bottom: 0.8rem;
              }

              .member-name {
                font-size: 2rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: $text-primary;
                position: relative;
                display: inline-block;

                @media (max-width: 768px) {
                  font-size: 1.6rem;
                  margin-bottom: 0.3rem;
                  &::after {
                    left: 50%;
                    transform: translateX(-50%);
                    width: 2rem;
                    height: 0.15rem;
                    bottom: 0;
                  }
                }
              }

              .member-career {
                color: $primary-color;
                font-size: 1.2rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                background: linear-gradient(135deg, $primary-color, rgba($primary-color, 0.7));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

                @media (max-width: 768px) {
                  font-size: 1rem;
                  letter-spacing: 0.03em;
                }
              }
            }

            .member-extra {
              font-size: 1.1rem;
              color: $text-secondary;
              margin-bottom: 1.5rem;
              letter-spacing: 0.02em;
              opacity: 0.85;
              line-height: 1.6;

              @media (max-width: 768px) {
                text-align: center;
                margin-bottom: 0.8rem;
                font-size: 0.95rem;
                line-height: 1.5;
                letter-spacing: 0.01em;
                padding: 0 0.5rem;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: -0.4rem;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 40px;
                  height: 1px;
                  background: rgba($primary-color, 0.2);
                }
              }
            }
          }

          .member-intro-wrapper {
            .member-intro {
              font-size: 1.1rem;
              line-height: 1.8;
              color: $text-secondary;
              padding: 1.5rem;
              background: linear-gradient(
                  135deg,
                  rgba(255, 255, 255, 0.9),
                  rgba(255, 255, 255, 0.5)
              );
              border-radius: 15px;
              position: relative;

              &::before {
                content: "\"";
                position: absolute;
                top: -1rem;
                left: -0.5rem;
                font-size: 4rem;
                color: rgba($primary-color, 0.1);
                font-family: serif;
              }

              @media (max-width: 768px) {
                text-align: center;
                background: rgba(255, 255, 255, 0.9);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
                padding: 1.2rem;
                font-size: 0.95rem;
                line-height: 1.6;
                margin-top: 0.5rem;

                &::before {
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 3rem;
                  top: -0.8rem;
                }
              }
            }
          }
        }
      }
    }
  }

  // 4. Advantages Section
  .advantages-section {
    background-color: #fff;
    overflow: hidden;
    width: 100%;
    padding: 6rem 0;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(-45deg, rgba($primary-color, 0.03), rgba($primary-color, 0.01), rgba(255, 255, 255, 0.02), rgba($primary-color, 0.02));
      background-size: 400% 400%;
      animation: gradient 15s ease infinite;
      z-index: 0;
    }

    .container {
      position: relative;
      z-index: 1;
    }

    .section-title {
      margin-bottom: 5rem;
      text-align: center;
    }

    .advantages-list {
      max-width: 1200px;
      margin: 0 auto;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        height: 100%;
        width: 1px;
        background: linear-gradient(180deg,
          rgba($primary-color, 0) 0%,
          rgba($primary-color, 0.1) 15%,
          rgba($primary-color, 0.1) 85%,
          rgba($primary-color, 0) 100%
        );
      }
    }

    .advantage-item {
      position: relative;
      margin-bottom: 8rem;
      padding-left: 0;
      display: flex;
      align-items: center;
      gap: 3rem;
      width: 90%;

      &:last-child {
        margin-bottom: 0;
      }

      &.right-aligned {
        margin-left: auto;
        flex-direction: row-reverse;

        .advantage-number {
          align-items: flex-start;

          &::after {
            left: auto;
            right: -2rem;
            transform: translateX(100%);
          }
        }

        .advantage-content {
          &::before {
            left: auto;
            right: -2rem;
            transform: translateX(50%) rotate(45deg);
          }
        }
      }

      @media (max-width: 768px) {
        width: 100%;
        margin-left: 0 !important;
        flex-direction: column !important;
        gap: 1.5rem;
        margin-bottom: 4rem;
        padding: 0 1rem;

        .advantage-number {
          align-items: center !important;

          &::after {
            display: none;
          }
        }

        .advantage-content {
          &::before {
            display: none;
          }
        }
      }
    }

    .advantage-number {
      display: flex;
      align-items: flex-end;
      position: relative;
      padding: 1rem;

      span {
        font-size: 6rem;
        font-weight: 700;
        line-height: 1;
        background: linear-gradient(135deg, rgba($primary-color, 0.8), rgba($primary-color, 0.2));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: "Helvetica Neue", sans-serif;
        letter-spacing: -0.02em;
      }

      &::after {
        content: "";
        position: absolute;
        left: -2rem;
        top: 50%;
        width: 4rem;
        height: 1px;
        background: linear-gradient(90deg,
          rgba($primary-color, 0.1),
          rgba($primary-color, 0.3) 50%,
          rgba($primary-color, 0.1)
        );
        transform: translateX(-100%);
      }

      @media (max-width: 768px) {
        span {
          font-size: 4rem;
        }
      }
    }

    .advantage-content {
      position: relative;
      padding: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.95) 100%);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
      transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
      flex: 1;

      .content-wrapper {
        display: flex;
        align-items: center;

        &.flex-row-reverse {
          flex-direction: row-reverse;
        }
      }

      .text-content {
        flex: 1;
        padding: 3rem;
      }

      .image-content {
        flex: 0 0 40%;
        border-radius: 0 15px 15px 0;
        overflow: hidden;
        transition: all 0.4s ease;

        img {
          width: 100%;
          height: 280px;
          object-fit: cover;
          transition: transform 0.6s ease;
          display: block;
        }
      }

      .flex-row-reverse {
        .image-content {
          border-radius: 15px 0 0 15px;
        }
      }

      .plane-connector {
        position: absolute;
        left: -2rem;
        top: 50%;
        z-index: 2;
        transform: translateX(-50%) translateY(-50%);
        font-size: 1.4rem;
        color: $primary-color;
        opacity: 0.8;
        transition: all 0.3s ease;
        color: $primary-color;

        &.right-side {
          left: auto;
          right: -2rem;
          transform: translateX(50%) translateY(-50%) rotate(180deg);
        }

        @media (max-width: 1024px) {
          display: none;
        }
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);

        .image-content img {
          transform: scale(1.05);
        }

        .plane-connector {
          opacity: 1;
          animation: planeFloat 2s ease-in-out infinite;

          &.right-side {
            animation: planeFloatRight 2s ease-in-out infinite;
          }
        }
      }

      @media (max-width: 1024px) {
        .content-wrapper {
          flex-direction: column !important;
        }

        .image-content {
          flex: 0 0 auto;
          width: 100%;
          border-radius: 15px 15px 0 0 !important;

          img {
            height: 240px;
          }
        }

        .text-content {
          padding: 2rem;
          order: 2;
        }
      }

      @media (max-width: 768px) {
        .text-content {
          padding: 1.5rem;
        }

        .image-content img {
          height: 200px;
        }
      }

      .advantage-header {
        margin-bottom: 1.5rem;

        .advantage-title {
          font-size: 2rem;
          font-weight: 600;
          color: $text-primary;
          margin: 0;
          line-height: 1.2;

          @media (max-width: 768px) {
            font-size: 1.6rem;
          }
        }
      }

      .advantage-description {
        font-size: 1.1rem;
        line-height: 1.8;
        color: $text-secondary;
      }
    }
  }

  // 5. Vision Section
  .vision-section {
    padding: 120px 0;
    position: relative;
    text-align: center;
    color: #fff;
    background-image: url("@/assets/images/about/vision_bg.jpg");
    background-size: cover;
    background-position: center;
    background-attachment: fixed; // Parallax effect

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(10, 25, 47, 0.7); // Dark blue overlay
    }

    .vision-content {
      position: relative;
      z-index: 1;
      max-width: 800px;
      margin: 0 auto;
      padding: 0 20px;

      .section-title {
        color: #fff;

        &::after {
          background-color: #fff;
        }
      }

      p {
        font-size: 1.3rem;
        line-height: 1.8;
        margin-bottom: 2rem;
        font-weight: 300;

        @media (max-width: 768px) {
          font-size: 1.1rem;
        }
      }

      .tagline {
        font-size: 1.5rem;
        font-weight: 500;
        font-style: italic;
        margin-top: 2rem;
        line-height: 1.6;

        @media (max-width: 768px) {
          font-size: 1.2rem;
        }
      }

      .action-wrapper {
        margin-top: 3rem;

        .consult-btn {
          display: inline-flex;
          align-items: center;
          gap: 1rem;
          background: $primary-color;
          color: #fff;
          padding: 1rem 2.5rem;
          border-radius: 50px;
          font-size: 1.2rem;
          font-weight: 500;
          text-decoration: none;
          transition: all 0.3s ease;
          box-shadow: 0 5px 15px rgba($primary-color, 0.3);

          i {
            transition: transform 0.3s ease;
          }

          &:hover {
            background: color.adjust($primary-color, $lightness: -10%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba($primary-color, 0.4);

            i {
              transform: translateX(5px);
            }
          }

          @media (max-width: 768px) {
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
          }
        }
      }
    }
  }

  // Decorative floating shapes
  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(209, 18, 66, 0.1), rgba(209, 18, 66, 0.05));
    filter: blur(20px);
    z-index: -1;
    pointer-events: none;

    &:nth-child(1) {
      width: 300px;
      height: 300px;
      top: 10%;
      right: -150px;
      animation: float 8s ease-in-out infinite;
    }

    &:nth-child(2) {
      width: 200px;
      height: 200px;
      bottom: 20%;
      left: -100px;
      animation: float 6s ease-in-out infinite;
      animation-delay: -2s;
    }

    &:nth-child(3) {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 10%;
      animation: float 7s ease-in-out infinite;
      animation-delay: -1s;
    }
  }

}

// Responsive adjustments
@media (max-width: 768px) {
  .about-page {
    .section-title {
      font-size: 2rem;
    }

    .content-section {
      padding: 50px 0;
    }

    .vision-section {
      padding: 80px 0;
      background-attachment: scroll; // 移动端移除parallax效果
    }

    .hero-section .hero-content {
      padding: 25vh 5% 0; // 调整移动端padding

      h1 {
        font-size: 2.5rem;
      }

      p {
        font-size: 1.1rem;
      }
    }

    .intro-section .intro-text {
      font-size: 1rem;
    }

    .advantages-section .advantage-item {
      .advantage-row,
      .advantage-row.row-reverse {
        flex-direction: column;
      }

      .advantage-image {
        height: 250px;
        margin-bottom: 30px;
      }

      .advantage-content {
        padding: 0;
        text-align: center;
      }
    }
  }

}
