<template lang="pug">
.about-page
  // Add floating shapes
  .floating-shape(v-for="i in 3" :key="i")

  // Section 1: Hero Section
  .hero-section
    .hero-background
    .hero-content
      h1(data-aos="fade-right") {{t(hero.title)}}
      p(data-aos="fade-right" data-aos-delay="200") {{t(hero.subtitle)}}

  // Section 2: Company Introduction
  .intro-section.content-section#introSection
    .container
      h2.section-title(data-aos="fade-up") {{t(intro.title)}}
      .intro-content(data-aos="fade-up" data-aos-delay="200")
        .intro-main
          .intro-header
            .intro-text-wrapper
              .text-container
                p.intro-text.highlight {{t(intro.subtitle1)}}
                p.intro-text {{t(intro.subtitle2)}}
            .main-image
              img(:src="office1" alt="Pan American Jet" draggable="false")
        .intro-gallery(data-aos="fade-up")
          h3.gallery-title(data-aos="fade-up") {{t('app.about.intro.environment')}}
          Swiper.office-swiper(
            :modules="[SwiperAutoplay, SwiperPagination, SwiperNavigation]"
            :slides-per-view="4"
            :space-between="20"
            :loop="true"
            :autoplay="{ delay: 3000, disableOnInteraction: false }"
            :pagination="{ clickable: true }"
            :navigation="true"
            :breakpoints="{ 320: { slidesPerView: 1.2, spaceBetween: 10 }, 640: { slidesPerView: 2, spaceBetween: 15 }, 768: { slidesPerView: 3, spaceBetween: 15 }, 1024: { slidesPerView: 4, spaceBetween: 20 } }"
          )
            SwiperSlide(v-for="(image, index) in officeImages" :key="index")
              .gallery-item
                img(:src="image" :alt="`Office ${index + 2}`" draggable="false")

  // Section 3: Management Team
  .team-section.content-section#teamSection
    .container
      h2.section-title(data-aos="fade-up") {{t('app.about.team.title')}}
      template(v-if="isMobile")
        // Team Members For Mobile
        .team-grid
          .team-member(v-for="(member, index) in teamMembers"
            :key="member.id"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
            :class="{ 'reverse-layout': index % 2 !== 0 }")
            .member-image-section
              .member-image-container
                img.member-avatar(:src="member.image" :alt="t(member.name)")
            .member-content-section
              .content-wrapper
                .member-info
                  .title-area
                    h3.member-name {{t(member.name)}}
                    p.member-career {{t(member.career)}}
                .member-intro-wrapper
                  p.member-intro
                    p(v-for="item in member.intro" :key="item.id") {{t(item.text)}}

      template(v-else)
        // CEO Card for PC
        .ceo-card(v-for="(member, index) in teamMembers"
          :key="member.id"
          data-aos="fade-up"
          :data-aos-delay="index * 100"
          :class="{ 'reverse-layout': index % 2 !== 0 }")
          .ceo-content
            .ceo-image-wrapper
              img.ceo-avatar(:src="member.image" :alt="t(member.name)")
            .ceo-details
              h3.ceo-name {{t(member.name)}}
              p.ceo-career {{t(member.career)}}
              .ceo-intro
                p(v-for="item in member.intro" :key="item.id") {{t(item.text)}}
  // Section 4: Competitive Advantages
  .advantages-section.content-section
    .container
      h2.section-title(data-aos="fade-up") {{t('app.about.advantages.title')}}
      .advantages-list
        .advantage-item(v-for="(advantage, index) in advantages" :key="advantage.title"
          data-aos="fade-up"
          :data-index="String(index + 1).padStart(2, '0')"
          :class="{ 'right-aligned': index % 2 !== 0 }")
          .advantage-number
            span {{ String(index + 1).padStart(2, '0') }}
          .advantage-content
            .content-wrapper(:class="{ 'flex-row-reverse': index % 2 !== 0 }")
              .text-content
                .advantage-header
                  h3.advantage-title {{ t(advantage.title) }}
                .advantage-description {{ t(advantage.description) }}
              .image-content
                img(draggable="false" :src="advantage.image" :alt="t(advantage.title)")
            .plane-connector(:class="{ 'right-side': index % 2 !== 0 }")
              font-awesome-icon(:icon="['fas', 'plane']")

  // Section 5: Development Vision
  .vision-section
    .vision-content
      h2.section-title(data-aos="zoom-in") {{t(vision.title)}}
      p(data-aos="fade-up" data-aos-delay="200") {{t(vision.subtitle1)}}
      p.tagline(data-aos="fade-up" data-aos-delay="400") {{t(vision.subtitle2)}}
      .action-wrapper(data-aos="fade-up" data-aos-delay="600")
        router-link.consult-btn(to="/contact")
          span {{t('app.home.contact.btn')}}
  StructuredData(type="breadcrumb" :data="{breadcrumb: route.meta.breadcrumb,baseUrl: 'https://www.panamericanjet.com'}")
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import AOS from 'aos'
import 'aos/dist/aos.css'
import {useI18n} from 'vue-i18n'
import {Swiper, SwiperSlide} from 'swiper/vue'
import {Autoplay, Pagination, Navigation} from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
import office1 from '@/assets/images/about/webp/office1.webp'
import office2 from '@/assets/images/about/webp/office2.webp'
import office3 from '@/assets/images/about/webp/office3.webp'
import office4 from '@/assets/images/about/webp/office4.webp'
import office5 from '@/assets/images/about/webp/office5.webp'
import office6 from '@/assets/images/about/webp/office6.webp'
import office7 from '@/assets/images/about/webp/office7.webp'
import advantage1 from '@/assets/images/about/webp/advantage1.webp'
import advantage2 from '@/assets/images/about/webp/advantage2.webp'
import advantage3 from '@/assets/images/about/webp/advantage4.webp'
import StructuredData from '@/components/StructuredData.vue'
import {useRoute} from 'vue-router'
import {teamMembers} from './conf'

const {t} = useI18n()
const route = useRoute()
const SwiperAutoplay = Autoplay
const SwiperPagination = Pagination
const SwiperNavigation = Navigation
const isMobile = ref(false)
const isTablet = ref(false)

const checkDeviceType = () => {
  // 在SSR环境中，默认为桌面端
  if (typeof window === 'undefined') {
    isMobile.value = false
    isTablet.value = false
    return
  }

  // Check for touch capability
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // Get screen dimensions considering orientation
  const width = window.innerWidth
  const height = window.innerHeight

  // Check for mobile device characteristics
  const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

  // Determine device type based on multiple factors
  if (isMobileDevice && hasTouch) {
    if (width <= 768 || (width <= 1024 && height > width)) {
      isMobile.value = true
      isTablet.value = false
    } else if (width <= 1024) {
      isMobile.value = false
      isTablet.value = true
    } else {
      isMobile.value = false
      isTablet.value = false
    }
  } else {
    isMobile.value = width <= 768
    isTablet.value = width > 768 && width <= 1024
  }
}
// Initial check
checkDeviceType()

onMounted(() => {
  // 只在客户端初始化AOS
  if (typeof window !== 'undefined') {
    AOS.init({
      duration: 800,
      once: true
    })
  }
})

const hero = ref({
  title: 'app.about.banner.title',
  subtitle: 'app.about.banner.subtitle'
})

const intro = ref({
  title: 'app.about.intro.title',
  subtitle1: 'app.about.intro.desc1',
  subtitle2: 'app.about.intro.desc2'
})

const vision = ref({
  title: 'app.about.vision.title',
  subtitle1: 'app.about.vision.desc1',
  subtitle2: 'app.about.vision.desc2'
})

const advantages = ref([
  {
    icon: 'fa-shield-halved',
    title: 'app.about.advantages.aTitle1',
    description: 'app.about.advantages.aDesc1',
    image: advantage1
  },
  {
    icon: 'fa-globe',
    title: 'app.about.advantages.aTitle2',
    description: 'app.about.advantages.aDesc2',
    image: advantage2
  },
  {
    icon: 'fa-lock',
    title: 'app.about.advantages.aTitle3',
    description: 'app.about.advantages.aDesc3',
    image: advantage3
  }
])

const officeImages = ref([
  office2,
  office3,
  office4,
  office5,
  office6,
  office7
])

const timelineProgress = ref(0)

const setTimelineProgress = (progress: number) => {
  timelineProgress.value = progress
}
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
