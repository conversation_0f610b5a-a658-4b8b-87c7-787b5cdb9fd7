.licenses-page {
  padding: 40px 20px 0;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-top: 70px;
    margin-bottom: 40px;
    .main-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }

    .subtitle {
      font-size: 1.1rem;
      color: #606266;
      font-weight: 300;
      letter-spacing: 0.5px;
    }
  }

  .content-card {
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

    .card-header {
      font-size: 1.25rem;
      font-weight: 500;
      color: #303133;
    }

    .company-intro {
      p {
        line-height: 1.8;
        font-size: 1rem;
        color: #606266;
        text-indent: 2em;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        font-weight: 500;
      }
    }
  }

  // Media query for mobile devices
  @media (max-width: 768px) {
    & {
      padding: 20px 15px 0;
    }

    .page-header {
      margin-bottom: 30px;

      .main-title {
        font-size: 1.8rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .content-card {
      .card-header {
        font-size: 1.1rem;
      }

      .company-intro p {
        font-size: 0.95rem;
        text-indent: 1.5em;
      }

      :deep(.el-descriptions-item__cell),
      :deep(.el-descriptions-item__label) {
        font-size: 0.9rem;
        padding: 8px 10px;
      }
    }
  }
}
