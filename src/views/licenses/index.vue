<template lang="pug">
.licenses-page
  .page-header
    h1.main-title {{t('app.licenses.title')}}
    p.subtitle {{t('app.licenses.subTitle')}}
  el-row(:gutter="20" justify="center")
    el-col(:lg="16" :md="20" :sm="22" :xs="24")
      el-card.content-card
        template(#header)
          .card-header
            span {{t('app.licenses.card1.title')}}
        .company-intro
          p {{t('app.licenses.card1.cont1')}}
          p {{t('app.licenses.card1.cont2')}}
          p {{t('app.licenses.card1.cont3')}}
  el-row(:gutter="20" justify="center")
    el-col(:lg="16" :md="20" :sm="22" :xs="24")
      el-card.content-card
        template(#header)
          .card-header
            span {{t('app.licenses.card2.title')}}
        el-descriptions(:column="1" border)
          el-descriptions-item(:label="t('app.licenses.card2.cnameLabel')") {{t('app.licenses.card2.cname')}}
          el-descriptions-item(:label="t('app.licenses.card2.addressLabel')") {{t(contactInfo.address)}}
          el-descriptions-item(:label="t('app.licenses.card2.scopeLabel')") {{t('app.licenses.card2.scope')}}
          el-descriptions-item(:label="t('app.licenses.card2.authorityLabel')") {{t('app.licenses.card2.authority')}}
          //-el-descriptions-item(label="营业执照副本")
            el-link(
              type="primary"
              href="/res/licenses.pdf"
              target="_blank"
              rel="noopener noreferrer"
            ) {{t('app.licenses.card2.view')}}
</template>
<script setup lang="ts">
import {useI18n} from 'vue-i18n'
import {contactInfo} from '@/conf'
const {t} = useI18n()
</script>
<style scoped lang="scss">
@use "./index.scss" as *;
</style>
