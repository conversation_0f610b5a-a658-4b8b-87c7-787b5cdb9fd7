@use "sass:color";
/* src/views/services/index.scss */

@keyframes kenburns {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

// Color Palette
$primary-color: #1a1a1a; // Dark background
$secondary-color: #f0f0f0; // Light text
$accent-color: #D11242; // Gold accent
$card-bg-color: #2c2c2c; // Card background

.services-page {

  .section {
    padding: 80px 5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    @media (max-width: 768px) {
      padding: 30px 5%;
    }
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: $secondary-color;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background-color: $accent-color;
    }
    @media (max-width: 768px) {
      font-size: 1.8rem;
    }
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: #a0a0a0;
    max-width: 800px;
    margin-bottom: 50px;
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }

  // Hero Section
  .hero-section {
    height: 100vh;
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    text-align: left;
    color: #fff;
    position: relative;
    padding: 0;
    overflow: hidden;

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      animation: kenburns 20s ease-out infinite alternate;
      z-index: 1;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, transparent 60%);
      z-index: 2;
    }

    .hero-content {
      position: relative;
      z-index: 3;
      max-width: 600px;
      margin-left: 10%;
      margin-bottom: 10vh;
      text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
    }

    h1 {
      font-size: 4rem;
      font-weight: 700;
      margin-bottom: 20px;
      text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
    }

    p {
      font-size: 1.5rem;
      font-weight: 300;
      margin-bottom: 40px;
      text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.7);
    }

    .el-button--primary {
      background-color: $accent-color;
      border-color: $accent-color;
      color: $secondary-color;
      font-size: 1rem;
      padding: 20px 40px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background-color: color.adjust($accent-color, $lightness: -10%);
        border-color: color.adjust($accent-color, $lightness: -10%);
      }
    }

    @media (max-width: 768px) {
      height: 75vh;
      .hero-content {
        h1 {
          font-size: 2.5rem;
        }

        p {
          font-size: 1.1rem;
        }
      }
    }

    @media (max-width: 480px) {
      height: 60vh; // 更小屏幕进一步降低高度
    }
  }

  // Services Section
  .services-section {
    padding-bottom: 0;
    background-color: #fff;

    .section-title {
      color: $primary-color;
    }

    .section-subtitle {
      color: #666;
    }

    .services-list {
      width: 100%;
      max-width: 1600px;
      display: flex;
      flex-direction: column;
      gap: 80px;
    }

    .service-item {
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      box-shadow: 0 10px 30px rgba(48, 55, 66, 0.07);
      overflow: hidden;
      border-radius: 12px;
      transition: all 0.3s ease-in-out;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 18px 45px rgba(48, 55, 66, 0.15);
      }

      @media (max-width: 992px) {
        flex-direction: column !important;
      }

      &.reverse {
        flex-direction: row-reverse;

        .service-content {
          border-left: none;
          border-right: 4px solid $accent-color;
        }
      }

      .service-image, .service-content {
        flex: 1;
        width: 50%;
        @media (max-width: 992px) {
          width: 100%;
        }
      }

      .service-image {
        img {
          width: 100%;
          height: 100%;
          display: block;
          object-fit: cover;
          min-height: 400px;
        }
      }

      .service-content {
        padding: 40px 60px;
        text-align: left;
        border-left: 4px solid $accent-color;

        .service-icon {
          font-size: 2.5rem;
          color: $accent-color;
          margin-bottom: 20px;
        }

        h3 {
          font-size: 2rem;
          color: #333;
          font-weight: 600;
          margin-bottom: 20px;
        }

        p {
          font-size: 1.1rem;
          color: #666;
          line-height: 1.8;
        }

        .read-more {
          display: inline-block;
          margin-top: 20px;
          color: $accent-color;
          text-decoration: none;
          font-weight: 600;
          position: relative;
          transition: all 0.3s ease;
          cursor: pointer;

          &::after {
            content: "→";
            margin-left: 8px;
            transition: transform 0.3s ease;
          }

          &:hover {
            color: color.adjust($accent-color, $lightness: -10%);

            &::after {
              transform: translateX(5px);
            }
          }
        }

        @media (max-width: 767px) {
          padding: 0;
          border-left: none !important;
          border-right: none !important;
          border-top: 4px solid $accent-color;
          h3, p {
            padding: 0 20px;
          }
          h3 {
            font-size: 1.5rem;
          }
          p {
            font-size: 1rem;
          }
          .read-more {
            display: block;
            margin: 0 auto 20px;
            text-align: center;
          }
        }
      }
    }
  }

  // CTA Section
  .cta-section {
    margin-top: 80px;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url("@/assets/images/service/cta-bg.jpg") no-repeat center center;
    background-size: cover;
    background-attachment: fixed;

    .section-subtitle {
      color: #fff;
      text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.7);
    }

    .el-button--primary {
      background-color: $accent-color;
      border-color: $accent-color;
      color: $secondary-color;
      font-size: 1rem;
      padding: 20px 40px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background-color: color.adjust($accent-color, $lightness: -10%);
        border-color: color.adjust($accent-color, $lightness: -10%);
      }
    }
  }
}

