import charterImg from '@/assets/images/service/webp/service1.webp'
import leaseImg from '@/assets/images/service/webp/service0.webp'
import assetImg from '@/assets/images/service/webp/service3.webp'
import conciergeImg from '@/assets/images/service/service4_3.jpg'
import motorcade1 from '@/assets/images/service/motorcade/motorcade1.jpg'
import motorcade2 from '@/assets/images/service/motorcade/motorcade1_2.jpg'
import motorcade3 from '@/assets/images/service/motorcade/motorcade2.jpg'
import motorcade4 from '@/assets/images/service/motorcade/motorcade3.jpg'
import motorcade5 from '@/assets/images/service/motorcade/motorcade4.jpg'
import motorcade6 from '@/assets/images/service/motorcade/motorcade4_1.jpg'
import motorcade7 from '@/assets/images/service/motorcade/motorcade4_2.jpg'
import motorcade8 from '@/assets/images/service/motorcade/motorcade5.jpg'
import motorcade9 from '@/assets/images/service/motorcade/motorcade5_1.jpg'
import motorcade10 from '@/assets/images/service/motorcade/motorcade5_2.jpg'
import motorcade11 from '@/assets/images/service/motorcade/motorcade5_11.jpg'

import { markRaw } from 'vue'
import { Connection, Place, Position, Promotion, Ship, Star, Suitcase, Van, Location, Opportunity, Timer, TakeawayBox, Postcard, DataAnalysis, Refresh } from '@element-plus/icons-vue'

export const charter = {
  title: 'app.serviceDetail.charter.title',
  desc: 'app.serviceDetail.charter.desc',
  overview: {
    title: 'app.serviceDetail.charter.overview.title',
    content: 'app.serviceDetail.charter.overview.content',
    img: charterImg
  },
  coreServices: {
    title: 'app.serviceDetail.charter.coreServices.title',
    charterTypes: {
      title: 'app.serviceDetail.charter.coreServices.sectionTitle',
      list: [
        {
          title: 'app.serviceDetail.charter.coreServices.section1Title',
          details: ['app.serviceDetail.charter.coreServices.section1Details1', 'app.serviceDetail.charter.coreServices.section1Details2']
        },
        {
          title: 'app.serviceDetail.charter.coreServices.section2Title',
          details: ['app.serviceDetail.charter.coreServices.section2Details1', 'app.serviceDetail.charter.coreServices.section2Details2']
        },
        {
          title: 'app.serviceDetail.charter.coreServices.section3Title',
          details: ['app.serviceDetail.charter.coreServices.section3Details1']
        }
      ]
    },
    fleet: {
      title: 'app.serviceDetail.charter.fleet.title',
      list: [
        {
          name: 'app.serviceDetail.charter.fleet.section1Title',
          details: 'app.serviceDetail.charter.fleet.section1Details1',
          examples: 'app.serviceDetail.charter.fleet.section1Details2',
          awesomeIcon: ['fas', 'plane']
        },
        {
          name: 'app.serviceDetail.charter.fleet.section2Title',
          details: 'app.serviceDetail.charter.fleet.section2Details1',
          examples: 'app.serviceDetail.charter.fleet.section2Details2',
          awesomeIcon: ['fas', 'plane-up']
        },
        {
          name: 'app.serviceDetail.charter.fleet.section3Title',
          details: 'app.serviceDetail.charter.fleet.section3Details1',
          examples: 'app.serviceDetail.charter.fleet.section3Details2',
          awesomeIcon: ['fas', 'plane-departure']
        },
        {
          name: 'app.serviceDetail.charter.fleet.section4Title',
          details: 'app.serviceDetail.charter.fleet.section4Details1',
          examples: 'app.serviceDetail.charter.fleet.section4Details2',
          awesomeIcon: ['fas', 'helicopter']
        }
      ]
    },
    customized: {
      title: 'app.serviceDetail.charter.customized.title',
      list: [
        { name: 'app.serviceDetail.charter.customized.section1Title', icon: markRaw(Ship) },
        { name: 'app.serviceDetail.charter.customized.section2Title', icon: markRaw(Star) },
        { name: 'app.serviceDetail.charter.customized.section3Title', icon: markRaw(Suitcase) },
        { name: 'app.serviceDetail.charter.customized.section4Title', icon: markRaw(Position) }
      ]
    }
  },
  advantages: {
    title: 'app.serviceDetail.charter.advantages.title',
    points: [
      { icon: '✔', title: 'app.serviceDetail.charter.advantages.section1Title', text: 'app.serviceDetail.charter.advantages.section1Details1' },
      { icon: '✔', title: 'app.serviceDetail.charter.advantages.section2Title', text: 'app.serviceDetail.charter.advantages.section2Details1' },
      { icon: '✔', title: 'app.serviceDetail.charter.advantages.section3Title', text: 'app.serviceDetail.charter.advantages.section3Details1' },
      { icon: '✔', title: 'app.serviceDetail.charter.advantages.section4Title', text: 'app.serviceDetail.charter.advantages.section4Details1' }
    ]
  },
  scenarios: {
    title: 'app.serviceDetail.charter.scenarios.title',
    items: [
      { title: 'app.serviceDetail.charter.scenarios.section1Title', text: 'app.serviceDetail.charter.scenarios.section1Details1' },
      { title: 'app.serviceDetail.charter.scenarios.section2Title', text: 'app.serviceDetail.charter.scenarios.section2Details1' },
      { title: 'app.serviceDetail.charter.scenarios.section3Title', text: 'app.serviceDetail.charter.scenarios.section3Details1' }
    ]
  },
  process: {
    title: 'app.serviceDetail.charter.process.title',
    steps: [
      { title: 'app.serviceDetail.charter.process.section1Title', text: 'app.serviceDetail.charter.process.section1Details1' },
      { title: 'app.serviceDetail.charter.process.section2Title', text: 'app.serviceDetail.charter.process.section2Details1' },
      { title: 'app.serviceDetail.charter.process.section3Title', text: 'app.serviceDetail.charter.process.section3Details1' },
      { title: 'app.serviceDetail.charter.process.section4Title', text: 'app.serviceDetail.charter.process.section4Details1' }
    ]
  },
  cta: {
    title: 'app.serviceDetail.charter.cta.title',
    btn: 'app.serviceDetail.charter.cta.btn'
  }
}
export const lease = {
  title: 'app.serviceDetail.lease.title',
  desc: 'app.serviceDetail.lease.desc',
  overview: {
    title: 'app.serviceDetail.lease.overview.title',
    content: 'app.serviceDetail.lease.overview.content',
    img: leaseImg
  },
  coreServices: {
    title: 'app.serviceDetail.lease.coreServices.title',
    charterTypes: {
      title: 'app.serviceDetail.lease.coreServices.sectionTitle',
      list: [
        {
          title: 'app.serviceDetail.lease.coreServices.section1Title',
          details: ['app.serviceDetail.lease.coreServices.section1Details1', 'app.serviceDetail.lease.coreServices.section1Details2']
        },
        {
          title: 'app.serviceDetail.lease.coreServices.section2Title',
          details: ['app.serviceDetail.lease.coreServices.section2Details1', 'app.serviceDetail.lease.coreServices.section2Details2']
        },
        {
          title: 'app.serviceDetail.lease.coreServices.section3Title',
          details: ['app.serviceDetail.lease.coreServices.section3Details1', 'app.serviceDetail.lease.coreServices.section3Details2']
        }
      ]
    },
    fleet: {
      title: 'app.serviceDetail.lease.fleet.title',
      list: [
        {
          name: 'app.serviceDetail.lease.fleet.section1Title',
          examples: 'app.serviceDetail.lease.fleet.section1Details1',
          awesomeIcon: ['fas', 'plane']
        },
        {
          name: 'app.serviceDetail.lease.fleet.section2Title',
          examples: 'app.serviceDetail.lease.fleet.section2Details1',
          awesomeIcon: ['fas', 'plane-up']
        },
        {
          name: 'app.serviceDetail.lease.fleet.section3Title',
          examples: 'app.serviceDetail.lease.fleet.section3Details1',
          awesomeIcon: ['fas', 'plane-departure']
        }
      ]
    },
    customized: {
      title: 'app.serviceDetail.lease.customized.title',
      list: [
        { name: 'app.serviceDetail.lease.customized.section1Title', icon: markRaw(Ship) },
        { name: 'app.serviceDetail.lease.customized.section2Title', icon: markRaw(Star) },
        { name: 'app.serviceDetail.lease.customized.section3Title', icon: markRaw(Suitcase) },
        { name: 'app.serviceDetail.lease.customized.section4Title', icon: markRaw(Position) }
      ]
    }
  },
  advantages: {
    title: 'app.serviceDetail.lease.advantages.title',
    points: [
      { icon: '✔', title: 'app.serviceDetail.lease.advantages.section1Title', text: 'app.serviceDetail.lease.advantages.section1Details1' },
      { icon: '✔', title: 'app.serviceDetail.lease.advantages.section2Title', text: 'app.serviceDetail.lease.advantages.section2Details1' },
      { icon: '✔', title: 'app.serviceDetail.lease.advantages.section3Title', text: 'app.serviceDetail.lease.advantages.section3Details1' },
      { icon: '✔', title: 'app.serviceDetail.lease.advantages.section4Title', text: 'app.serviceDetail.lease.advantages.section4Details1' }
    ]
  },
  scenarios: {
    title: 'app.serviceDetail.lease.scenarios.title',
    items: [
      { title: 'app.serviceDetail.lease.scenarios.section1Title', text: 'app.serviceDetail.lease.scenarios.section1Details1' },
      { title: 'app.serviceDetail.lease.scenarios.section2Title', text: 'app.serviceDetail.lease.scenarios.section2Details1' },
      { title: 'app.serviceDetail.lease.scenarios.section3Title', text: 'app.serviceDetail.lease.scenarios.section3Details1' }
    ]
  },
  process: {
    title: 'app.serviceDetail.lease.process.title',
    steps: [
      { title: 'app.serviceDetail.lease.process.section1Title', text: 'app.serviceDetail.lease.process.section1Details1' },
      { title: 'app.serviceDetail.lease.process.section2Title', text: 'app.serviceDetail.lease.process.section2Details1' },
      { title: 'app.serviceDetail.lease.process.section3Title', text: 'app.serviceDetail.lease.process.section3Details1' },
      { title: 'app.serviceDetail.lease.process.section4Title', text: 'app.serviceDetail.lease.process.section4Details1' },
      { title: 'app.serviceDetail.lease.process.section5Title', text: 'app.serviceDetail.lease.process.section5Details1' }
    ]
  },
  cta: {
    title: 'app.serviceDetail.lease.cta.title',
    btn: 'app.serviceDetail.lease.cta.btn'
  }
}
export const asset = {
  title: 'app.serviceDetail.asset.title',
  desc: 'app.serviceDetail.asset.desc',
  overview: {
    title: 'app.serviceDetail.asset.overview.title',
    content: 'app.serviceDetail.asset.overview.content',
    img: assetImg
  },
  coreServices: {
    title: 'app.serviceDetail.asset.coreServices.title',
    charterTypes: {
      title: 'app.serviceDetail.asset.coreServices.sectionTitle',
      list: [
        {
          title: 'app.serviceDetail.asset.coreServices.section1Title',
          details: ['app.serviceDetail.asset.coreServices.section1Details1', 'app.serviceDetail.asset.coreServices.section1Details2']
        },
        {
          title: 'app.serviceDetail.asset.coreServices.section2Title',
          details: ['app.serviceDetail.asset.coreServices.section2Details1', 'app.serviceDetail.asset.coreServices.section2Details2']
        },
        {
          title: 'app.serviceDetail.asset.coreServices.section3Title',
          details: ['app.serviceDetail.asset.coreServices.section3Details1', 'app.serviceDetail.asset.coreServices.section3Details2']
        }
      ]
    },
    fleet: {
      title: 'app.serviceDetail.asset.fleet.title',
      list: [
        {
          name: 'app.serviceDetail.asset.fleet.section1Title',
          examples: 'app.serviceDetail.asset.fleet.section1Details1',
          icon: markRaw(Postcard)
        },
        {
          name: 'app.serviceDetail.asset.fleet.section2Title',
          examples: 'app.serviceDetail.asset.fleet.section2Details1',
          icon: markRaw(DataAnalysis)
        },
        {
          name: 'app.serviceDetail.asset.fleet.section3Title',
          examples: 'app.serviceDetail.asset.fleet.section3Details1',
          icon: markRaw(Refresh)
        }
      ]
    },
    customized: {
      title: 'app.serviceDetail.asset.customized.title',
      list: [
        { name: 'app.serviceDetail.asset.customized.section1Title', desc: 'app.serviceDetail.asset.customized.section1Detail', icon: markRaw(Timer) },
        { name: 'app.serviceDetail.asset.customized.section2Title', desc: 'app.serviceDetail.asset.customized.section2Detail', icon: markRaw(TakeawayBox) }
      ]
    },
    specialScenes: {
      title: 'app.serviceDetail.asset.specialScenes.title',
      list: [
        { title: 'app.serviceDetail.asset.specialScenes.section1Title', text: 'app.serviceDetail.asset.specialScenes.section1Detail', icon: markRaw(Location) },
        { title: 'app.serviceDetail.asset.specialScenes.section2Title', text: 'app.serviceDetail.asset.specialScenes.section2Detail', icon: markRaw(Opportunity) }
      ]
    }
  },
  advantages: {
    title: 'app.serviceDetail.asset.advantages.title',
    points: [
      { icon: '✔', title: 'app.serviceDetail.asset.advantages.section1Title', text: 'app.serviceDetail.asset.advantages.section1Details1' },
      { icon: '✔', title: 'app.serviceDetail.asset.advantages.section2Title', text: 'app.serviceDetail.asset.advantages.section2Details1' },
      { icon: '✔', title: 'app.serviceDetail.asset.advantages.section3Title', text: 'app.serviceDetail.asset.advantages.section3Details1' },
      { icon: '✔', title: 'app.serviceDetail.asset.advantages.section4Title', text: 'app.serviceDetail.asset.advantages.section4Details1' }
    ]
  },
  scenarios: {
    title: 'app.serviceDetail.asset.scenarios.title',
    items: [
      { title: 'app.serviceDetail.asset.scenarios.section1Title', text: 'app.serviceDetail.asset.scenarios.section1Details1' },
      { title: 'app.serviceDetail.asset.scenarios.section2Title', text: 'app.serviceDetail.asset.scenarios.section2Details1' },
      { title: 'app.serviceDetail.asset.scenarios.section3Title', text: 'app.serviceDetail.asset.scenarios.section3Details1' }
    ]
  },
  process: {
    title: 'app.serviceDetail.asset.process.title',
    steps: [
      { title: 'app.serviceDetail.asset.process.section1Title', text: 'app.serviceDetail.asset.process.section1Details1' },
      { title: 'app.serviceDetail.asset.process.section2Title', text: 'app.serviceDetail.asset.process.section2Details1' },
      { title: 'app.serviceDetail.asset.process.section3Title', text: 'app.serviceDetail.asset.process.section3Details1' },
      { title: 'app.serviceDetail.asset.process.section4Title', text: 'app.serviceDetail.asset.process.section4Details1' }
    ]
  },
  cta: {
    title: 'app.serviceDetail.asset.cta.title',
    btn: 'app.serviceDetail.asset.cta.btn'
  }
}
export const concierge = {
  title: 'app.serviceDetail.concierge.title',
  desc: 'app.serviceDetail.concierge.desc',
  overview: {
    title: 'app.serviceDetail.concierge.overview.title',
    content: 'app.serviceDetail.concierge.overview.content',
    img: conciergeImg
  },
  coreServices: {
    title: 'app.serviceDetail.concierge.coreServices.title',
    charterTypes: {
      title: 'app.serviceDetail.concierge.coreServices.sectionTitle',
      list: [
        {
          title: 'app.serviceDetail.concierge.coreServices.section1Title',
          details: ['app.serviceDetail.concierge.coreServices.section1Details1', 'app.serviceDetail.concierge.coreServices.section1Details2']
        },
        {
          title: 'app.serviceDetail.concierge.coreServices.section2Title',
          details: ['app.serviceDetail.concierge.coreServices.section2Details1', 'app.serviceDetail.concierge.coreServices.section2Details2']
        },
        {
          title: 'app.serviceDetail.concierge.coreServices.section3Title',
          details: ['app.serviceDetail.concierge.coreServices.section3Details1']
        }
      ]
    },
    fleet: {
      title: 'app.serviceDetail.concierge.fleet.title',
      list: [
        {
          name: 'app.serviceDetail.concierge.fleet.section1Title',
          details: 'app.serviceDetail.concierge.fleet.section1Details1',
          examples: 'app.serviceDetail.concierge.fleet.section1Details2',
          awesomeIcon: ['fas', 'plane']
        },
        {
          name: 'app.serviceDetail.concierge.fleet.section2Title',
          details: 'app.serviceDetail.concierge.fleet.section2Details1',
          examples: 'app.serviceDetail.concierge.fleet.section2Details2',
          awesomeIcon: ['fas', 'plane-up']
        },
        {
          name: 'app.serviceDetail.concierge.fleet.section3Title',
          details: 'app.serviceDetail.concierge.fleet.section3Details1',
          examples: 'app.serviceDetail.concierge.fleet.section3Details2',
          awesomeIcon: ['fas', 'plane-departure']
        },
        {
          name: 'app.serviceDetail.concierge.fleet.section4Title',
          details: 'app.serviceDetail.concierge.fleet.section4Details1',
          examples: 'app.serviceDetail.concierge.fleet.section4Details2',
          awesomeIcon: ['fas', 'helicopter']
        }
      ]
    },
    customized: {
      title: 'app.serviceDetail.concierge.customized.title',
      list: [
        { name: 'app.serviceDetail.concierge.customized.section1Title', icon: markRaw(Ship) },
        { name: 'app.serviceDetail.concierge.customized.section2Title', icon: markRaw(Star) },
        { name: 'app.serviceDetail.concierge.customized.section3Title', icon: markRaw(Suitcase) },
        { name: 'app.serviceDetail.concierge.customized.section4Title', icon: markRaw(Position) }
      ]
    }
  },
  sliders: {
    title: 'app.serviceDetail.concierge.sliders.title',
    list: [
      {
        img: motorcade1
      },
      {
        img: motorcade2
      },
      {
        img: motorcade3
      },
      {
        img: motorcade4
      },
      {
        img: motorcade5
      },
      {
        img: motorcade6
      },
      {
        img: motorcade7
      },
      {
        img: motorcade8
      },
      {
        img: motorcade9
      },
      {
        img: motorcade10
      },
      {
        img: motorcade11
      }
    ]
  },
  advantages: {
    title: 'app.serviceDetail.concierge.advantages.title',
    points: [
      { icon: '✔', title: 'app.serviceDetail.concierge.advantages.section1Title', text: 'app.serviceDetail.concierge.advantages.section1Details1' },
      { icon: '✔', title: 'app.serviceDetail.concierge.advantages.section2Title', text: 'app.serviceDetail.concierge.advantages.section2Details1' },
      { icon: '✔', title: 'app.serviceDetail.concierge.advantages.section3Title', text: 'app.serviceDetail.concierge.advantages.section3Details1' },
      { icon: '✔', title: 'app.serviceDetail.concierge.advantages.section4Title', text: 'app.serviceDetail.concierge.advantages.section4Details1' }
    ]
  },
  scenarios: {
    title: 'app.serviceDetail.concierge.scenarios.title',
    items: [
      { title: 'app.serviceDetail.concierge.scenarios.section1Title', text: 'app.serviceDetail.concierge.scenarios.section1Details1' },
      { title: 'app.serviceDetail.concierge.scenarios.section2Title', text: 'app.serviceDetail.concierge.scenarios.section2Details1' },
      { title: 'app.serviceDetail.concierge.scenarios.section3Title', text: 'app.serviceDetail.concierge.scenarios.section3Details1' }
    ]
  },
  process: {
    title: 'app.serviceDetail.concierge.process.title',
    steps: [
      { title: 'app.serviceDetail.concierge.process.section1Title', text: 'app.serviceDetail.concierge.process.section1Details1' },
      { title: 'app.serviceDetail.concierge.process.section2Title', text: 'app.serviceDetail.concierge.process.section2Details1' },
      { title: 'app.serviceDetail.concierge.process.section3Title', text: 'app.serviceDetail.concierge.process.section3Details1' },
      { title: 'app.serviceDetail.concierge.process.section4Title', text: 'app.serviceDetail.concierge.process.section4Details1' }
    ]
  },
  cta: {
    title: 'app.serviceDetail.concierge.cta.title',
    btn: 'app.serviceDetail.concierge.cta.btn'
  }
}
