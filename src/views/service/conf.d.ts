export interface Charter {
  title: string;
  desc: string;
  overview: {
    title: string;
    content: string;
    img: string;
  };
  coreServices: {
    title: string;
    charterTypes: {
      title: string;
      list: Array<{
        title: string;
        details: string[];
      }>;
    };
    fleet: {
      title: string;
      list: Array<{
        name: string;
        details: string;
        examples: string;
        icon: any;
      }>;
    };
    customized: {
      title: string;
      list: Array<{
        name: string;
        icon: any;
      }>;
    };
  };
  advantages: {
    title: string;
    points: Array<{
      icon: string;
      title: string;
      text: string;
    }>;
  };
  scenarios: {
    title: string;
    items: Array<{
      title: string;
      text: string;
    }>;
  };
  process: {
    title: string;
    steps: Array<{
      title: string;
      text: string;
    }>;
  };
  cta: {
    title: string;
    btn: string;
  };
}

export const charter: Charter;
