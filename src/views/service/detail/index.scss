@use "sass:color";

// Variables
$primary-color: #D11242;
$text-color: #333;
$light-text-color: #666;
$background-color: #f8f9fa;
$white-color: #fff;
$dark-bg-color: #1a1a1a;
$border-color: #eee;

// Global Styles
.services-detail-page {
  background-color: $white-color;
  color: $text-color;
  overflow-x: hidden;

  :deep(.swiper) {

    .swiper-button-next,
    .swiper-button-prev {
      color: $primary-color;
    }

    .swiper-pagination-bullet {
      &-active {
        background: $primary-color;
      }
    }
  }
}

h2.section-title,
h3.sub-section-title {
  text-align: center;
  font-weight: 600;
  margin-bottom: 40px;
  color: $text-color;
  display: block;
  transform: none;
  left: auto;
}

h2.section-title {
  text-align: center;
  font-weight: 600;
  margin-bottom: 40px;
  color: $text-color;
  font-size: 2.2rem;
  position: relative;
  width: 100%;

  &::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: $primary-color;
  }
}

h3.sub-section-title {
  font-size: 1.8rem;
  color: $light-text-color;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;

  .section-title {
    display: inline-block;
    margin: 0 auto;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background-color: $primary-color;
    }
  }
}

// Common Section Styles
.service-section {
  position: relative;
  padding: 80px 0;

  .section-title {
    position: relative;
    text-align: center;
    font-size: 2.2rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 60px;

    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(to right, transparent, $primary-color, transparent);
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 40px;
    }
  }
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

// Hero Section
.hero {
  position: relative;
  min-height: 500px;
  padding: 120px 20px 80px;
  overflow: hidden;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
  color: $white-color;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    min-height: 400px;
    padding: 100px 20px;
  }

  @media (max-width: 480px) {
    min-height: 350px;
    padding: 80px 15px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 30%, rgba($primary-color, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(41, 98, 255, 0.15) 0%, transparent 50%);
    animation: gradientShift 15s ease-in-out infinite;

    @media (max-width: 768px) {
      background:
        radial-gradient(circle at 30% 30%, rgba($primary-color, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 70% 70%, rgba(41, 98, 255, 0.12) 0%, transparent 40%);
    }
  }

  // 背景装饰
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

    // 几何形状装饰
    .geometric-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .shape-1 {
        width: 380px;
        height: 380px;
        border-radius: 50%;
        top: -160px;
        right: -100px;
        background: conic-gradient(from 45deg,
            rgba($primary-color, 0.2) 0%,
            rgba(41, 98, 255, 0.2) 25%,
            rgba($primary-color, 0.15) 50%,
            rgba(41, 98, 255, 0.2) 75%,
            rgba($primary-color, 0.2) 100%);
        box-shadow:
          0 0 40px rgba($primary-color, 0.15),
          inset 0 0 60px rgba(41, 98, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(8px);
        animation:
          rotateGradient 15s linear infinite,
          pulseScale 8s ease-in-out infinite;
        opacity: 0.85;

        &::before {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: inherit;
          background: conic-gradient(from 225deg,
              rgba($primary-color, 0.1),
              rgba(41, 98, 255, 0.1),
              rgba($primary-color, 0.1),
              rgba(41, 98, 255, 0.1));
          animation: rotateGradient 12s linear infinite reverse;
          opacity: 0.5;
        }

        &::after {
          content: '';
          position: absolute;
          inset: 20px;
          border-radius: inherit;
          border: 1px solid rgba(255, 255, 255, 0.1);
          background: radial-gradient(circle at 30% 30%,
              rgba(255, 255, 255, 0.1) 0%,
              transparent 60%);
          animation: pulseOpacity 6s ease-in-out infinite;
        }

        @media (max-width: 768px) {
          width: 260px;
          height: 260px;
          top: -100px;
          right: -60px;

          &::after {
            inset: 15px;
          }
        }
      }

      .shape-2 {
        width: 240px;
        height: 240px;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        bottom: -80px;
        left: -60px;
        background: radial-gradient(circle at center,
            rgba(41, 98, 255, 0.2) 0%,
            rgba(41, 98, 255, 0) 70%);
        box-shadow:
          0 0 25px rgba(41, 98, 255, 0.2),
          inset 0 0 15px rgba($primary-color, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.15);
        animation:
          morphing 20s linear infinite,
          colorShift2 12s ease-in-out infinite;

        &::after {
          content: '';
          position: absolute;
          inset: -1px;
          border-radius: inherit;
          background: inherit;
          filter: blur(12px);
          opacity: 0.5;
          z-index: -1;
        }

        @media (max-width: 768px) {
          width: 180px;
          height: 180px;
          bottom: -50px;
          left: -40px;
        }
      }

      .shape-3 {
        width: 180px;
        height: 180px;
        border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg,
            rgba($primary-color, 0.25) 0%,
            rgba(41, 98, 255, 0.25) 100%);
        box-shadow:
          0 0 30px rgba($primary-color, 0.3),
          inset 0 0 20px rgba(41, 98, 255, 0.3);
        animation:
          waveFloat 8s ease-in-out infinite,
          colorShift3 10s ease-in-out infinite;
        backdrop-filter: blur(8px);
        border: 2px solid rgba(255, 255, 255, 0.2);

        @media (max-width: 768px) {
          width: 120px;
          height: 120px;
        }

        &::after {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: inherit;
          background: inherit;
          filter: blur(15px);
          opacity: 0.7;
          animation: glowPulse 4s ease-in-out infinite;
          z-index: -1;
        }
      }
    }
  }

  .hero-content {
    position: relative;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;

    .content-wrapper {
      position: relative;
      padding: 40px;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.03);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
      margin: 0 20px;
      width: 100%;

      @media (max-width: 768px) {
        padding: 30px 20px;
        margin: 0 15px;
      }

      @media (max-width: 480px) {
        padding: 25px 15px;
        margin: 0 10px;
      }
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      color: $white-color;
      letter-spacing: 2px;
      line-height: 1.2;
      position: relative;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      animation: titleReveal 1.5s ease-out forwards;

      @media (max-width: 768px) {
        font-size: 2.2rem;
        letter-spacing: 1px;
        margin-bottom: 1rem;
      }

      @media (max-width: 480px) {
        font-size: 1.8rem;
        letter-spacing: 0.5px;
        line-height: 1.3;
        margin-bottom: 0.8rem;
      }
    }

    .title-decoration {
      width: 120px;
      height: 4px;
      background: linear-gradient(90deg,
          transparent,
          rgba($primary-color, 0.8),
          rgba(41, 98, 255, 0.8),
          transparent);
      margin: 20px auto;
      position: relative;
      animation: decorationWidth 1.5s ease-out forwards;

      @media (max-width: 768px) {
        width: 100px;
        height: 3px;
        margin: 15px auto;
      }

      @media (max-width: 480px) {
        width: 80px;
        height: 2px;
        margin: 12px auto;
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: $primary-color;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        animation: glowPulse 2s infinite;

        @media (max-width: 768px) {
          width: 6px;
          height: 6px;
        }

        @media (max-width: 480px) {
          width: 4px;
          height: 4px;
        }
      }

      &::before {
        left: 15%;
        animation-delay: 0s;
      }

      &::after {
        right: 15%;
        animation-delay: 1s;
      }
    }

    .hero-subtitle {
      font-size: 1.25rem;
      font-weight: 400;
      color: rgba($white-color, 0.9);
      margin: 1rem auto 0;
      max-width: 800px;
      line-height: 1.8;
      letter-spacing: 0.5px;
      opacity: 0;
      animation: fadeInUp 1s ease-out 0.5s forwards;

      @media (max-width: 768px) {
        font-size: 1.1rem;
        line-height: 1.6;
        margin: 0.8rem auto 0;
        padding: 0 10px;
        max-width: 100%;
      }

      @media (max-width: 480px) {
        font-size: 1rem;
        line-height: 1.5;
        letter-spacing: 0.3px;
        margin: 0.6rem auto 0;
        padding: 0 5px;
      }
    }
  }
}

// 新增颜色变换动画
@keyframes colorShift1 {

  0%,
  100% {
    background: radial-gradient(circle at center,
        rgba($primary-color, 0.18) 0%,
        rgba($primary-color, 0) 70%);
    box-shadow:
      0 0 25px rgba($primary-color, 0.2),
      inset 0 0 15px rgba(41, 98, 255, 0.2);
  }

  50% {
    background: radial-gradient(circle at center,
        rgba(41, 98, 255, 0.18) 0%,
        rgba(41, 98, 255, 0) 70%);
    box-shadow:
      0 0 25px rgba(41, 98, 255, 0.2),
      inset 0 0 15px rgba($primary-color, 0.2);
  }
}

@keyframes colorShift2 {

  0%,
  100% {
    background: radial-gradient(circle at center,
        rgba(41, 98, 255, 0.2) 0%,
        rgba(41, 98, 255, 0) 70%);
    box-shadow:
      0 0 25px rgba(41, 98, 255, 0.2),
      inset 0 0 15px rgba($primary-color, 0.2);
  }

  50% {
    background: radial-gradient(circle at center,
        rgba($primary-color, 0.2) 0%,
        rgba($primary-color, 0) 70%);
    box-shadow:
      0 0 25px rgba($primary-color, 0.2),
      inset 0 0 15px rgba(41, 98, 255, 0.2);
  }
}

@keyframes colorShift3 {

  0%,
  100% {
    background: linear-gradient(45deg,
        rgba($primary-color, 0.25) 0%,
        rgba(41, 98, 255, 0.25) 100%);
    box-shadow:
      0 0 30px rgba($primary-color, 0.3),
      inset 0 0 20px rgba(41, 98, 255, 0.3);
  }

  50% {
    background: linear-gradient(45deg,
        rgba(41, 98, 255, 0.25) 0%,
        rgba($primary-color, 0.25) 100%);
    box-shadow:
      0 0 30px rgba(41, 98, 255, 0.3),
      inset 0 0 20px rgba($primary-color, 0.3);
  }
}

// 修改现有动画
@keyframes morphing {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: rotate(0deg) scale(1);
  }

  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    transform: rotate(90deg) scale(1.05);
  }

  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    transform: rotate(180deg) scale(1);
  }

  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    transform: rotate(270deg) scale(1.05);
  }

  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: rotate(360deg) scale(1);
  }
}

@keyframes floating {

  0%,
  100% {
    transform: translate(-50%, -50%);
  }

  50% {
    transform: translate(-50%, -60%);
  }
}

@keyframes pulseAndRotate {

  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.08) rotate(180deg);
    opacity: 0.95;
  }
}

@keyframes gradientShift {

  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes glowPulse {

  0%,
  100% {
    filter: blur(15px);
    opacity: 0.7;
  }

  50% {
    filter: blur(20px);
    opacity: 0.9;
  }
}

@keyframes titleReveal {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes decorationWidth {
  from {
    width: 0;
    opacity: 0;
  }

  to {
    width: 120px;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes waveFloat {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
  }

  25% {
    transform: translate(-65%, -55%) rotate(90deg) scale(1.15);
    border-radius: 37% 63% 46% 54% / 48% 55% 45% 52%;
  }

  50% {
    transform: translate(-50%, -65%) rotate(180deg) scale(1);
    border-radius: 54% 46% 63% 37% / 52% 45% 48% 55%;
  }

  75% {
    transform: translate(-35%, -55%) rotate(270deg) scale(1.15);
    border-radius: 46% 54% 37% 63% / 45% 52% 55% 48%;
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg) scale(1);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
  }
}

// 新的动画关键帧
@keyframes rotateGradient {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulseScale {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes pulseOpacity {

  0%,
  100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }

  50% {
    opacity: 0.6;
    transform: scale(1.02) rotate(180deg);
  }
}

// Overview Section
.overview {
  background: linear-gradient(to bottom, #fff 0%, rgba($background-color, 0.1) 100%);

  .section-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: $light-text-color;
  }

  .overview-image-col {
    display: flex;
    align-items: center;

    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
      }
    }
  }
}

// Image Slider Section
.image-slider {
  position: relative;
  background: linear-gradient(to bottom, #fff 0%, rgba($background-color, 0.3) 100%);
  padding: 80px 0;
  margin: 40px 0;
  overflow: hidden;

  :deep(.image-swiper) {
    padding: 0;
    position: relative;
    overflow: visible;
    height: 100%;
    user-select: none;

    // .swiper-wrapper {
    //   height: 100%;
    //   // transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
    // }

    .swiper-slide {
      width: 80%;
      position: relative;
      transform: scale(0.85);
      opacity: 0.6;
      z-index: 1;

      &.swiper-slide-active {
        opacity: 1;
        transform: scale(1);
        z-index: 2;
        .slide-image{
          box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
        }
      }
    }

    .swiper-button-next,
    .swiper-button-prev {
      color: $primary-color;

      &:hover {
        transform: scale(1.1);
      }
    }

    .swiper-button-next {
      right: 8px;
    }

    .swiper-button-prev {
      left: 8px;
    }

    .swiper-pagination {
      bottom: -40px;

      .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        background: transparent;
        border: 2px solid $primary-color;
        opacity: 0.6;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin: 0 4px;

        &-active {
          background: $primary-color;
          opacity: 1;
          width: 20px;
          border-radius: 4px;
        }

        &:hover {
          opacity: 0.8;
          background: rgba($primary-color, 0.3);
        }
      }
    }
  }

  .slide-image {
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.6s cubic-bezier(0.645, 0.045, 0.355, 1);
    background: $white-color;
    margin: 0;
    //width: auto;
    //max-width: 100%;
    height: 500px;
    object-fit: cover;
    // display: flex;
    // align-items: center;
    // justify-content: center;

    .el-image {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: cover;
      }
    }
  }

  @media (max-width: 1024px) {
    :deep(.image-swiper) {
      .swiper-pagination {
        bottom: -50px;
      }
    }

    // .slider-container {
    //   max-width: 90%;
    //   height: 400px;
    // }

     .slide-image {
       height: 380px;
     }
  }

  @media (max-width: 768px) {
    padding: 60px 0;
    margin: 30px 0;

    :deep(.image-swiper) {
      .swiper-pagination {
        bottom: -35px;
      }
    }
    // .slide-image {
    //   padding: 8px;
    //   height: 240px;
    // }
  }

  @media (max-width: 480px) {
    .slider-container {
      height: 230px;
    }

    .slide-image {
      height: 200px;
    }

    :deep(.image-swiper) {
      .swiper-pagination {
        bottom: 0;
      }
    }
  }
}

// Core Services Section
.core-services {
  background-color: $background-color;
  position: relative;
  padding: 80px 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 0% 0%, rgba($primary-color, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 100% 100%, rgba($primary-color, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }

  .section-title {
    display: block;
    text-align: center;
    width: 100%;
    transform: none;
    left: auto;
    margin: 0 auto 60px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(to right, transparent, $primary-color, transparent);
    }
  }

  .sub-section {
    margin-top: 60px;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 1px;
      background: linear-gradient(to right, transparent, rgba($primary-color, 0.1), transparent);
    }

    .sub-section-title {
      text-align: center;
      margin-bottom: 40px;
      color: color.adjust($text-color, $lightness: -10%);
      font-weight: 600;
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .section-title {
      margin-bottom: 40px;
    }

    .sub-section {
      margin-top: 40px;

      &:not(:last-child)::after {
        width: 80%;
        bottom: -20px;
      }
    }
  }
}

// Charter Types
.charter-card {
  height: 100%;
  padding: 40px 30px;
  text-align: left;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: $primary-color;
    opacity: 0.8;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(209, 18, 66, 0.1) 0%, rgba(209, 18, 66, 0) 100%);
    border-radius: 50%;
    transform: translate(50%, -50%);
    z-index: 0;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);

    .charter-title {
      color: $primary-color;
    }
  }

  .charter-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 25px;
    text-align: left;
    position: relative;
    transition: color 0.3s ease;
    padding-bottom: 15px;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 40px;
      height: 3px;
      background: $primary-color;
      opacity: 0.3;
    }

    .main-text {
      display: inline-block;
      margin-right: 5px;
    }

    .en-text {
      display: inline-block;
      font-size: 0.9rem;
      color: $light-text-color;
      font-weight: normal;
    }
  }

  .charter-details {
    list-style-type: none;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;

    li {
      margin-bottom: 15px;
      color: $light-text-color;
      position: relative;
      padding-left: 28px;
      line-height: 1.6;
      text-align: left;

      &:last-child {
        margin-bottom: 0;
      }

      &::before {
        content: "✓";
        color: $primary-color;
        position: absolute;
        left: 0;
        font-weight: bold;
        opacity: 0.8;
      }

      &:hover {
        color: $text-color;

        &::before {
          opacity: 1;
        }
      }
    }
  }
}

// Aircraft Fleet
.aircraft-fleet {
  background: linear-gradient(to bottom, rgba($background-color, 0.5), rgba($background-color, 0.8));
  padding: 40px 0;

  .fleet-swiper {
    padding: 20px 10px 50px;
    max-width: 1200px;
    margin: 0 auto;
  }

  :deep(.swiper) {

    .swiper-button-next,
    .swiper-button-prev {
      color: $primary-color;

      &::after {
        font-size: 24px;
      }

      &:hover {
        color: color.adjust($primary-color, $lightness: -10%);
      }
    }

    .swiper-wrapper {
      justify-content: center;

      @media (max-width: 1023px) {
        justify-content: flex-start;
      }
    }

    .swiper-pagination-bullet {
      background: $light-text-color;
      opacity: 0.5;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
      }
    }

    .swiper-pagination-bullet-active {
      background: $primary-color;
      opacity: 1;
      transform: scale(1.2);
    }

    .swiper-slide {
      height: 280px;
    }
  }

  .jet-card {
    background: $white-color;
    padding: 30px 20px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

      .jet-icon {
        background: rgba($primary-color, 0.1);
        color: $primary-color;
        transform: scale(1.05);
      }
    }
  }

  .jet-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: rgba($primary-color, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: $light-text-color;
  }

  .jet-info {
    padding: 15px 0 0;
  }

  .jet-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: $text-color;
    margin: 0 0 8px;
  }

  .jet-details {
    font-size: 1rem;
    color: $primary-color;
    font-weight: 500;
    margin-bottom: 6px;
  }

  .jet-examples {
    font-size: 0.9rem;
    color: $light-text-color;
    line-height: 1.4;
  }
}

// Customized Services
.customized-services,
.specialScenes-services {
  .service-list {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;
  }

  .service-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    background: $white-color;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .service-icon-wrapper {
        background: $primary-color;

        .el-icon {
          color: $white-color;
          transform: scale(1.1);
        }
      }
    }
  }

  .service-icon-wrapper {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: rgba($primary-color, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 12px;

    .el-icon {
      color: $primary-color;
      transition: all 0.3s ease;
    }
  }

  .service-content {
    .service-name {
      font-size: 1rem;
      font-weight: 500;
      color: $text-color;
      margin: 0;
      white-space: nowrap;
    }
  }
}

// Service Advantages
.advantages {
  background: linear-gradient(to bottom, rgba($background-color, 0.1) 0%, #fff 100%);

  .advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }

  .advantage-item {
    text-align: center;
    padding: 30px 20px;
    background: $white-color;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(to right, rgba($primary-color, 0.3), rgba($primary-color, 0.6));
      opacity: 0;
      transition: all 0.3s ease;
    }

    &:hover,
    &.mobile-active {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);

      &::before {
        opacity: 1;
      }
    }
  }

  .advantage-icon {
    font-size: 2rem;
    color: $primary-color;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .advantage-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 10px 0;
    color: $text-color;
  }

  .advantage-text {
    color: $light-text-color;
    line-height: 1.6;
  }

  @media (max-width: 1024px) {
    .advantages-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

// Scenarios Section
.scenarios {
  background: linear-gradient(to bottom, #fff 0%, rgba($background-color, 0.2) 100%);

  .scenario-card {
    background: $white-color;
    padding: 30px;
    border-radius: 12px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, rgba($primary-color, 0.3), rgba($primary-color, 0.6));
      opacity: 0.6;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .scenario-title {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: $text-color;
    font-weight: 600;
  }

  .scenario-text {
    color: $light-text-color;
    line-height: 1.6;
  }
}

// Process Section
.process {
  background: linear-gradient(to bottom, rgba($background-color, 0.2) 0%, #fff 100%);

  .process-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding-left: 60px;

    &::after {
      content: "";
      position: absolute;
      width: 2px;
      background: linear-gradient(to bottom, rgba($primary-color, 0.2), rgba($primary-color, 0.4));
      top: 0;
      bottom: 0;
      left: 30px;
    }
  }

  .timeline-item {
    padding: 20px 30px;
    position: relative;
    background: $white-color;
    border-radius: 12px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateX(5px);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);

      .timeline-step {
        transform: scale(1.1) translateY(-50%);
        box-shadow: 0 0 0 4px rgba($primary-color, 0.2);
      }
    }
  }

  .timeline-step {
    position: absolute;
    left: -60px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: $primary-color;
    color: $white-color;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    z-index: 1;
    transition: all 0.3s ease;
  }

  .timeline-content {
    position: relative;
  }

  .timeline-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 10px;
  }

  .timeline-text {
    color: $light-text-color;
    line-height: 1.6;
  }
}

// CTA Section
.cta {
  position: relative;
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
  color: #fff;
  background-image: url("@/assets/images/about/vision_bg.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  isolation: isolate;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 25, 47, 0.2); // Dark blue overlay
    backdrop-filter: blur(5px);
  }

  .cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .cta-title {
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 600;
    color: #fff;
    line-height: 1.4;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .el-button--danger {
    background-color: $primary-color;
    border-color: $primary-color;
    color: $white-color;
    padding: 14px 42px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border-radius: 50px;
    font-weight: 500;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba($primary-color, 0.3);

    &:hover {
      background-color: color.adjust($primary-color, $lightness: -5%);
      border-color: color.adjust($primary-color, $lightness: -5%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba($primary-color, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba($primary-color, 0.2);
    }
  }

  @media (max-width: 768px) {
    .cta-content {
      padding: 60px 15px;
    }

    .cta-title {
      font-size: 1.8rem;
      margin-bottom: 30px;
    }

    .el-button--danger {
      padding: 12px 36px;
      font-size: 1rem;
    }
  }
}

// Responsive Styles
@media (max-width: 768px) {
  h2.section-title {
    font-size: 1.6rem;
  }

  .hero {
    padding: 60px 10px 40px 10px;

    .hero-title {
      font-size: 1.6rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }

    &::after {
      margin: 24px auto 0 auto;
    }
  }

  .overview {
    .el-col {
      text-align: center;
    }

    .overview-image-col {
      margin-top: 30px;
    }
  }

  .process-timeline {
    &::after {
      left: 20px;
    }

    .timeline-item {
      padding: 10px 0 10px 50px;
    }

    .timeline-step {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }
  }

  .charter-card {
    padding: 30px 20px;
    margin-bottom: 20px;

    .charter-title {
      font-size: 1.2rem;
    }
  }

  .customized-services,
  .specialScenes-services {
    .service-list {
      gap: 15px;
    }

    .service-item {
      padding: 12px 20px;
    }

    .service-icon-wrapper {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }

    .service-name {
      font-size: 0.9rem;
    }
  }

  .cta {
    .cta-content {
      padding: 60px 15px;
    }

    .cta-title {
      font-size: 1.6rem;
      margin-bottom: 30px;
    }

    .el-button--danger {
      padding: 12px 36px;
      font-size: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .service-section {
    padding: 20px 0;

    h2.section-title {
      font-size: 1.4rem;
    }

    .page-content {
      padding: 0 15px;
    }

    .charter-card {
      margin-bottom: 20px;
    }

  }

  .customized-services .service-item,
  .specialScenes-services .service-item {
    padding: 10px;
  }

  .cta {
    .cta-content {
      padding: 40px 15px;
    }

    .cta-title {
      font-size: 1.4rem;
      margin-bottom: 25px;
    }

    .el-button--danger {
      padding: 10px 30px;
      width: 80%;
      max-width: 280px;
    }
  }
}
