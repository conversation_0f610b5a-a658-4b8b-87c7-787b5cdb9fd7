<template lang="pug">
.services-detail-page
  //- Hero Section
  section.hero
    .hero-background
      .geometric-shapes
        .shape.shape-1(data-aos="fade-right" data-aos-duration="1000")
        .shape.shape-2(data-aos="fade-left" data-aos-duration="1200")
        .shape.shape-3(data-aos="fade-up" data-aos-duration="1400")
    .hero-content
      .content-wrapper(data-aos="fade-up" data-aos-duration="1000")
        h1.hero-title {{t(pageData.title)}}
        .title-decoration
        p.hero-subtitle(data-aos="fade-up" data-aos-delay="200") {{t(pageData.desc)}}
      .hero-overlay

  .page-content
    //- Service Overview
    section.service-section.overview
      el-row(:gutter=40)
        el-col(:xs="24" :md="12" data-aos="fade-right")
          h2.section-title {{ t(pageData.overview.title) }}
          p.section-text {{ t(pageData.overview.content) }}
        el-col.overview-image-col(:xs="24" :md="12" data-aos="fade-left")
          img(:src="pageData.overview.img" alt="泛美公务机")

    //- Core Services
    section.service-section.core-services
      h2.section-title(data-aos="fade-up") {{ t(pageData.coreServices.title) }}

      //- Charter Types
      .sub-section.charter-types
        h3.sub-section-title(data-aos="fade-up") {{ t(pageData.coreServices.charterTypes.title) }}
        el-row(:gutter="30")
          el-col(:xs="24" :sm="24" :md="8" v-for="item in pageData.coreServices.charterTypes.list" :key="item.title")
            el-card.charter-card(shadow="never" data-aos="fade-up")
              h3.charter-title
                span.main-text {{ t(item.title) }}
              ul.charter-details
                li(v-for="detail in item.details" :key="detail") {{ t(detail) }}

      //- Aircraft Fleet
      .sub-section.aircraft-fleet(data-aos="fade-up")
        h3.sub-section-title(data-aos="fade-up") {{ t(pageData.coreServices.fleet.title) }}
        swiper.fleet-swiper(
          :modules="[Navigation, Pagination]"
          :slides-per-view="1"
          :space-between="5"
          :navigation="true"
          :autoHeight="false"
          :pagination="{ clickable: true }"
          :breakpoints="{ 640: { slidesPerView: 1 }, 768: { slidesPerView: 2 }, 1024: { slidesPerView: 4 } }"
        )
          swiper-slide(v-for="jet in pageData.coreServices.fleet.list" :key="jet.name")
            .jet-card(data-aos="fade-up")
              .jet-icon
                el-icon(:size="48")
                  font-awesome-icon(v-if="jet.awesomeIcon" :icon="jet.awesomeIcon")
                  component(v-else :is="jet.icon")
              .jet-info
                h4.jet-name {{ t(jet.name) }}
                p.jet-details(v-if="jet.details") {{ t(jet.details) }}
                p.jet-examples {{ t(jet.examples) }}

      //- Customized Services
      .sub-section.customized-services(data-aos="fade-up")
        h3.sub-section-title(data-aos="fade-up") {{ t(pageData.coreServices.customized.title) }}
        .service-list
          .service-item(v-for="service in pageData.coreServices.customized.list" :key="service.name" data-aos="zoom-in")
            .service-icon-wrapper
              el-icon(:size="28")
                font-awesome-icon(v-if="service.awesomeIcon" :icon="service.awesomeIcon")
                component(v-else :is="service.icon")
            .service-content
              h4.service-name {{ t(service.name) }}
              .service-desc(v-if="service.desc") {{ t(service.desc) }}

      //- Special Scenes Services
      .sub-section.specialScenes-services(v-if="pageData.coreServices.specialScenes" data-aos="fade-up")
        h3.sub-section-title(data-aos="fade-up") {{ t(pageData.coreServices.specialScenes.title) }}
        .service-list
          .service-item(v-for="service in pageData.coreServices.specialScenes.list" :key="service.name" data-aos="zoom-in")
            .service-icon-wrapper
              el-icon(:size="28")
                font-awesome-icon(v-if="service.awesomeIcon" :icon="service.awesomeIcon")
                component(v-else :is="service.icon")
            .service-content
              h4.service-name {{ t(service.title) }}
              .service-desc {{ t(service.text) }}

    //- Image Slider Section
    section.service-section.image-slider(v-if="pageData.sliders")
      h2.section-title(data-aos="fade-up") {{ t(pageData.sliders.title) }}
      .slider-container(data-aos="fade-up")
        swiper.image-swiper(
          :modules="[Navigation, Pagination, Autoplay]"
          :grabCursor="true"
          :centeredSlides="true"
          :slidesPerView="isMobile? 1 :'auto'"
          :space-between="0"
          :navigation="true"
          :pagination="{ clickable: true }"
          :loop="true"
          :speed="600"
          :effect="'coverflow'"
          :allowTouchMove="true"
          :preventInteractionOnTransition="true"
          :autoplay="{ delay: 5000, disableOnInteraction: false }"
        )
          swiper-slide(v-for="(slide, index) in pageData.sliders.list" :key="index")
            .slide-image
              el-image(
                :src="slide.img"
                fit="cover"
                loading="lazy"
                draggable="false"
              )

    //- Service Advantages
    section.service-section.advantages
      .section-header
        h2.section-title(data-aos="fade-up") {{ t(pageData.advantages.title) }}
      .advantages-grid
        .advantage-item(v-for="adv in pageData.advantages.points" :key="adv.title" data-aos="fade-up" :class="{ 'mobile-active': !isDesktop }")
          .advantage-icon {{ t(adv.icon) }}
          h3.advantage-title {{ t(adv.title) }}
          p.advantage-text {{ t(adv.text) }}

    //- Applicable Scenarios
    section.service-section.scenarios
      .section-header
        h2.section-title(data-aos="fade-up") {{ t(pageData.scenarios.title) }}
      el-row(:gutter="30")
        el-col(:xs="24" :md="8" v-for="scenario in pageData.scenarios.items" :key="scenario.title" data-aos="zoom-in")
          .scenario-card
            h3.scenario-title {{ t(scenario.title) }}
            p.scenario-text {{ t(scenario.text) }}

    //- Service Process
    section.service-section.process
      h2.section-title(data-aos="fade-up") {{ t(pageData.process.title) }}
      .process-timeline
        .timeline-item(v-for="(step, index) in pageData.process.steps" :key="step.title" data-aos="fade-right" :data-aos-delay="index * 150")
          .timeline-step {{ '0' + (index + 1) }}
          .timeline-content
            h3.timeline-title {{ t(step.title) }}
            p.timeline-text {{ t(step.text) }}

    //- CTA Section
    section.service-section.cta
      .cta-content(data-aos="flip-up")
        h2.cta-title {{ t(pageData.cta.title) }}
        el-button(type="danger" size="large" round @click="router.push({name: 'contact'})") {{ t(pageData.cta.btn) }}

</template>

<script setup lang="ts">
import {onMounted, ref, computed, markRaw} from 'vue'

// Import Swiper Vue.js components
import {Swiper, SwiperSlide} from 'swiper/vue'
import {Navigation, Pagination, Autoplay} from 'swiper/modules'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

// Import AOS
import AOS from 'aos'
import 'aos/dist/aos.css'
import * as pageDataConf from '@/views/service/conf'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import { useDeviceType } from '@/composables/useDeviceType'

const { isDesktop, isMobile } = useDeviceType()
const {t} = useI18n()
const route = useRoute()
const router = useRouter()
onMounted(() => {
  AOS.init({
    duration: 800,
    once: true
  })
})
const pageData = computed(() => {
  const id = route.params.id as string
  return (pageDataConf as any)[id] || {}
})
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
