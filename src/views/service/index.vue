<template lang="pug">
.services-page
  // Hero Section
  section.hero-section
    .hero-background(:style="{ backgroundImage: `url(${hero.image})` }")
    .hero-content
      h1(data-aos="fade-up") {{ t(hero.title) }}
      p(data-aos="fade-up" data-aos-delay="200") {{ t(hero.subtitle) }}
      el-button(type="primary" size="large" @click="scrollToServices" data-aos="fade-up" data-aos-delay="400") {{ t(hero.cta) }}

  // Core Services Section
  section.services-section.section#services
    h2.section-title {{ t(services.title) }}
    p.section-subtitle {{ t(services.subtitle) }}
    .services-list
      .service-item(v-for="(service, index) in services.items" :key="index" :class="{ 'reverse': index % 2 !== 0 }")
        .service-image(:data-aos="index % 2 === 0 ? 'fade-right' : 'fade-left'")
          img(:src="service.image" :alt="service.title")
        .service-content(:data-aos="index % 2 === 0 ? 'fade-left' : 'fade-right'")
          .service-icon
            i.fas(:class="service.icon")
          h3 {{ t(service.title) }}
          p {{ t(service.description) }}
          a.read-more(v-if="service.more" @click="handleReadMore(service)") {{t('app.service.viewMore')}}

  // Call to Action Section
  section.cta-section.section
    h2.section-title(data-aos="fade-up") {{t( cta.title )}}
    p.section-subtitle(data-aos="fade-up" data-aos-delay="100") {{t( cta.subtitle )}}
    el-button(type="primary" size="large" data-aos="fade-up" data-aos-delay="200" @click="router.push({name: 'contact'})") {{t( cta.cta )}}

  StructuredData(type="breadcrumb" :data="{breadcrumb: route.meta.breadcrumb,baseUrl: 'https://www.panamericanjet.com'}")
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import AOS from 'aos'
// import {useHeaderScroll} from '@/composables/useHeaderScroll'
import {useI18n} from 'vue-i18n'
import banner from '@/assets/images/service/webp/banner.webp'
import service0 from '@/assets/images/service/webp/service0.webp'
import service1 from '@/assets/images/service/webp/service1.webp'
import service2 from '@/assets/images/service/service2.png'
import service3 from '@/assets/images/service/webp/service3.webp'
import service4 from '@/assets/images/service/service4.jpg'
import service5 from '@/assets/images/service/webp/service5.webp'
import {useRoute, useRouter} from 'vue-router'
import StructuredData from '@/components/StructuredData.vue'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const hero = ref({
  title: 'app.service.banner.title',
  subtitle: 'app.service.banner.subtitle',
  cta: 'app.service.banner.btn',
  image: banner
})

const services = ref({
  title: 'app.service.coreServiceTitle',
  subtitle: 'app.service.coreServiceSubtitle',
  items: [
    {
      icon: 'fa-plane-departure',
      title: 'app.service.services.title0',
      description: 'app.service.services.desc0',
      image: service0,
      rid: 'lease',
      more: true
    },
    {
      icon: 'fa-plane-departure',
      title: 'app.service.services.title1',
      description: 'app.service.services.desc1',
      image: service1,
      rid: 'charter',
      more: true
    },
    {
      icon: 'fa-plane-circle-check',
      title: 'app.service.services.title2',
      description: 'app.service.services.desc2',
      image: service2,
      rid: 'management'
    },
    {
      icon: 'fa-chart-line',
      title: 'app.service.services.title3',
      description: 'app.service.services.desc3',
      image: service3,
      rid: 'asset',
      more: true
    },
    {
      icon: 'fa-concierge-bell',
      title: 'app.service.services.title4',
      description: 'app.service.services.desc4',
      image: service4,
      rid: 'concierge',
      more: true
    },
    {
      icon: 'fa-user-graduate',
      title: 'app.service.services.title5',
      description: 'app.service.services.desc5',
      image: service5,
      rid: 'training'
    }
  ]
})

const cta = ref({
  title: 'app.service.cta.title',
  subtitle: 'app.service.cta.subtitle',
  cta: 'app.service.cta.btn'
})

const scrollToServices = () => {
  const element = document.getElementById('services')
  if (element) {
    element.scrollIntoView({behavior: 'smooth'})
  }
}

// Use the composable to handle header scroll effect
// useHeaderScroll('#services')

onMounted(() => {
  if (typeof window !== 'undefined') {
    AOS.init({
      duration: 1000,
      once: true
    })
  }
})
function handleReadMore (server:any) {
  const routeData = router.resolve({ name: 'serviceDetail', params: { id: server.rid } })
  if (typeof window !== 'undefined') {
    window.open(routeData.href, '_blank')
  }
}
</script>

<style scoped lang="scss">
@use "./index.scss" as *;
</style>
