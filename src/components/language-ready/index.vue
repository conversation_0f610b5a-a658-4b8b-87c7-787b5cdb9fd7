<template lang="pug">
.language-ready-wrapper
  slot
.language-loading(v-show="!isLanguageReady")
  loading-animation
</template>

<script setup lang="ts">
import {onMounted, watch} from 'vue'
import {useLanguageLoading} from '@/composables/useLanguageLoading'
import LoadingAnimation from '../loading-animation/index.vue'
import {useLanguageStore} from '@/stores/language'

const {isLanguageReady, ensureLanguageLoaded} = useLanguageLoading()

const languageStore = useLanguageStore()
onMounted(() => {
  ensureLanguageLoaded()
})
watch(() => languageStore.lang, (val) => {
  ensureLanguageLoaded()
}, {
  immediate: true,
  deep: true
})
</script>

<style lang="scss" scoped>
.language-ready-wrapper {
  width: 100%;
  height: 100%;

}

.language-loading {
  position: fixed;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  top: 0;
  left: 0;
  z-index: 1000;
}
</style>
