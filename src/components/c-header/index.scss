@use "sass:color";

$primary-color: #D11242;
.c-header-wrapper {
  width: 100%;
}

.c-header {
  //background: #fff;
  background: transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 9;
  backdrop-filter: blur(8px);
  padding: 0 1rem;
  transition: background-color 0.3s ease;

  &.header-scrolled {
    background-color: #fff;

    .actions-container .language-selector-dropdown {
      .translate-icon {
        font-size: 1.25rem;
        color: var(--text-color);
      }
    }

    .menu-button {
      .hamburger-button {
        .line {
          background-color: var(--text-color);
        }
      }
    }
  }

  /* 移动端菜单按钮 */
  .menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 50px;
    transition: 0.4s;
    padding: 15px 10px;
    @media (min-width: 768px) {
      //display: none;
    }

    &:hover {
      background: rgba(255, 255, 255, .4);

      .hamburger-button {
        .line {
          width: 100%;
          background: var(--primary-color);

          &.line1 {
            //transform: translate(22px, 4px) rotate(35deg);
            //border-radius: 7px;
            //border-top-right-radius: 10px;
            //border-bottom-right-radius: 10px;
          }

          &.line2 {
            //width: 70%;
          }

          &.line3 {
            //transform: translate(22px, -4px) rotate(-35deg);
            //border-radius: 7px;
            //border-top-right-radius: 10px;
            //border-bottom-right-radius: 10px;
          }
        }
      }

      &.left {
        .hamburger-button {
          .line {
            width: 30%;

            &.line1 {
              transform: translate(0, 12px) rotate(-35deg);
              border-radius: 7px;
              border-top-left-radius: 10px;
              border-bottom-left-radius: 10px;
            }

            &.line2 {
              margin-left: 32px;
            }

            &.line3 {
              transform: translate(0, -12px) rotate(35deg);
              border-radius: 7px;
              border-top-left-radius: 10px;
              border-bottom-left-radius: 10px;
            }
          }
        }
      }
    }

    .hamburger-button {
      box-sizing: border-box;
      position: relative;
      width: 100%;
      height: 25px;
      cursor: pointer;

      .line {
        width: 100%;
        height: 3px;
        background-color: #e2e1e1;
        border-radius: 3px;
        position: absolute;
        transition: 0.4s;
      }

      .line1 {
        top: 0;
      }

      .line2 {
        top: calc(40%);
        width: 60%;
      }

      .line3 {
        top: calc(80%);
        width: 30%;
      }
    }
  }

  /* Logo样式 */
  .logo-container {
    flex-shrink: 0;
    max-width: 200px;

    .logo-link {
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .logo-text {
      font-size: 1.25rem;
      background: linear-gradient(135deg, var(--primary-color), #6d28d9);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    img {
      width: 100%;
    }

    @media (max-width: 768px) {
      max-width: 135px;
    }
  }

  /* 主导航 */
  .main-nav {
    display: none;
    margin: 0 auto;

    @media (min-width: 768px) {
      display: flex;
      align-items: center;
      gap: 2rem;
      justify-content: center;
    }
  }

  .nav-link {
    padding: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
    text-decoration: none;

    &:hover,
    &.router-link-active {
      color: var(--primary-color);
    }

    &.router-link-active::after {
      content: "";
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--primary-color);
      border-radius: 2px;
    }
  }

  /* 操作区域 */
  .actions-container {
    display: flex;
    align-items: center;
    gap: 1.25rem;

    .contact-btn {
      &:hover {
        color: #fff;
        background: color.adjust($primary-color, $lightness: 10%);
        border: 1px solid var(--primary-color);
      }
    }

    .language-selector-dropdown {
      .language-selector {
        cursor: pointer;
        display: flex;
        align-items: center;

        &:focus-visible {
          outline: none;
        }
      }

      .translate-icon {
        transition: all 0.2s ease;
        font-size: 1.25rem;
        color: #fff;
      }

      &:focus-visible,
      .translate-icon:focus-visible {
        outline: none;
      }

    }

    @media (max-width: 768px) {
      gap: 0.5rem;

      .contact-btn {
        padding: 4px 8px;
        font-size: 12px;
      }

      .language-selector-dropdown .translate-icon {
        font-size: 1.1rem;
      }
    }
  }

  .theme-icon {
    font-size: 1.25rem;
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    color: #fff;
  }

  .theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    transition: all 0.2s ease;
    background-color: transparent;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
      transform: rotate(15deg);
    }
  }

  .avatar-container {
    cursor: pointer;
  }

  .login-container {
    margin-left: 0.5rem;
  }
}

.btn-link {
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 6px;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
  line-height: 1.5;

  &:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 遮罩层 */
.menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9;
}

/* 移动端导航菜单 */
.mobile-menu {
  display: flex;
  flex-direction: column;
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  @media (min-width: 1200px) {
    width: 420px;
    padding: 20px;
  }

  @media (max-width: 768px) {
    width: 85%;
  }

  .main-cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;

    @media (min-width: 1200px) {
      padding: 0;
    }
  }

  .header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    @media (min-width: 1200px) {
      padding: 20px;
    }

    .logo {
      height: 40px;
      object-fit: contain;
      margin: 0 auto;

      @media (min-width: 1200px) {
        height: 55px;
      }
    }

    .mobile-menu-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: transparent;
      transition: all 0.3s ease;

      @media (min-width: 1200px) {
        width: 48px;
        height: 48px;
      }

      &:hover {
        background: rgba($primary-color, 0.05);

        .close-btn {
          color: var(--primary-color);
          transform: rotate(90deg);
        }
      }

      .close-btn {
        font-size: 20px;
        color: var(--text-color);
        transition: all 0.3s ease;
        cursor: pointer;

        @media (min-width: 1200px) {
          font-size: 24px;
        }
      }
    }
  }

  .mobile-menu-items {
    margin-top: 20px;
    padding: 0 10px;

    @media (min-width: 1200px) {
      margin-top: 40px;
      padding: 0 20px;
    }

    .nav-link-mobile {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
      border-radius: 8px;
      transition: all 0.3s ease;
      text-decoration: none;

      @media (min-width: 1200px) {
        font-size: 20px;
        padding: 16px 24px;
        margin-bottom: 16px;
      }

      &:hover,
      &.router-link-active {
        background: rgba($primary-color, 0.05);
        color: var(--primary-color);
        transform: translateX(5px);
      }
    }
  }

  .extra-links {
    margin-top: auto;
    padding: 20px 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    @media (min-width: 1200px) {
      padding: 30px 20px;
      gap: 24px;
    }

    .item {
      position: relative;
      color: var(--text-color);
      font-size: 14px;
      cursor: pointer;
      padding: 5px 0;

      @media (min-width: 1200px) {
        font-size: 16px;
        padding: 8px 0;
      }

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 2px;
        background: var(--primary-color);
        transform: scaleX(0);
        transition: transform 0.3s ease;
        transform-origin: right;
      }

      &:hover {
        color: var(--primary-color);

        &::after {
          transform: scaleX(1);
          transform-origin: left;
        }
      }

      a {
        color: inherit;
        text-decoration: none;
      }
    }
  }

  .footer-cont {
    padding: 20px;
    background: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);

    @media (min-width: 1200px) {
      padding: 30px;
    }

    .contact-link {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 15px;

      @media (min-width: 1200px) {
        gap: 20px;
        margin-bottom: 25px;
      }

      .link {
        color: var(--text-color);
        text-decoration: none;
        transition: color 0.3s ease;
        font-size: 14px;

        @media (min-width: 1200px) {
          font-size: 16px;
        }

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .address {
      font-size: 12px;
      color: var(--text-color-light);
      opacity: 0.8;
      font-style: italic;
      line-height: 1.5;

      @media (min-width: 1200px) {
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }
}

/* 过渡动画 */
.mask-fade-enter-active,
.mask-fade-leave-active {
  transition: opacity 0.3s ease;
}

.mask-fade-enter-from,
.mask-fade-leave-to {
  opacity: 0;
}

.slide-menu-enter-active,
.slide-menu-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-menu-enter-from,
.slide-menu-leave-to {
  transform: translateX(-100%);
}


