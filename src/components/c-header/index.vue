<template lang="pug">
.c-header-wrapper
  .c-header(:class="{'header-scrolled': isScrolled}")
    .container.mx-auto.px-4
      .flex.justify-between.items-center.h-16
        // 移动端菜单按钮
        .menu-button(@click="toggleMobileMenu")
          .hamburger-button
            .line.line1
            .line.line2
            .line.line3
        // Logo
        .logo-container
          img(:src="logo")
        // PC显示整个导航栏
        //nav.main-nav
        //  router-link.nav-link(
        //    v-for="item in navItems"
        //    :key="item.label"
        //    :to="item.to"
        //    :class="item.className||''"
        //  ) {{ $t(item.label) }}

        // 右侧操作区域
        .actions-container
          // Contact
          el-button.contact-btn(@click="handleContact") {{t('app.common.nav.contact')}}
          // 切换语言
          el-dropdown.language-selector-dropdown(trigger="click" @command="handleLanguageChange")
            span.language-selector
              el-icon.translate-icon
                font-awesome-icon(:icon="['fas', 'globe']")
            template(#dropdown)
              el-dropdown-menu
                el-dropdown-item.lang-option(v-for="item in langList" :key="item.value" :command="item.value" :class="{'active': item.value===languageStore.lang}") {{ item.label }}
          // 切换主题
          //button.theme-toggle(@click="toggleTheme")
          //  el-icon.theme-icon
          //    i-ep-sunny(v-if="isDarkMode")
          //    i-ep-moon(v-else)

  // 移动端导航菜单
  Transition(name="mask-fade")
    .menu-mask(v-if="mobileMenuOpen" @click="mobileMenuOpen = false")
  Transition(name="slide-menu")
    .mobile-menu(v-if="mobileMenuOpen")
      .main-cont
        .header-bar
          img.logo(:src="logo")
          .mobile-menu-close
            el-icon.close-btn(@click="mobileMenuOpen = false")
              i-ep-close-bold
        .mobile-menu-items
          router-link.nav-link-mobile(
            v-for="item in navItems"
            :key="item.label"
            :to="item.to"
            @click="mobileMenuOpen = false"
          ) {{ t(item.label) }}
        .extra-links
          .item(v-for="item in extraLinks" :key="item.id")
            a(v-if="item.isOutOfLink" :href="item.link" target="_blank") {{ t(item.label) }}
            span(v-else @click="handleClickExtraLink(item)") {{ t(item.label) }}
      .footer-cont
        .item.contact-link
          a.link(:href="`tel:${contactInfo.salesPhoneCountryCode}${contactInfo.salesPhone}`") {{`${contactInfo.salesPhoneCountryCode} ${contactInfo.salesPhone}`}}
          a.link(:href="`mailto:${contactInfo.salesEmail}`") {{contactInfo.salesEmail}}
        .item
          .text.address {{t('app.common.footer.address')}}
</template>

<script setup lang="ts">
import {ref, computed, reactive, onMounted, onUnmounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {useI18n} from 'vue-i18n'
import {setLocale} from '@/i18n'
import {useTheme} from '@/composables/useTheme'
import {useLanguageStore} from '@/stores/language'
import type {AvailableLocales} from '@/types/i18n.types'
import logoSrc from '@/assets/images/common/logo_whole.png'
import logoSmallSrc from '@/assets/images/common/logo.png'
import {contactInfo} from '@/conf'

const {t, locale} = useI18n()
const router = useRouter()
const route = useRoute()
const {currentTheme, toggleTheme} = useTheme()
const languageStore = useLanguageStore()
const scrollEventWitheList = ['contact', 'licenses', 'safety', 'serviceDetail', 'fleetDetail']
const isScrolled = ref(false)
const handleScroll = () => {
  if (typeof window !== 'undefined') {
    isScrolled.value = window.scrollY > 200
  }
}

onMounted(() => {
  if (scrollEventWitheList.includes(route.name as string)) {
    isScrolled.value = true
  } else if (typeof window !== 'undefined') {
    window.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (!scrollEventWitheList.includes(route.name as string) && typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll)
  }
})

// Logo
const logo = ref(logoSrc)
// 导航项
const navItems = reactive([
  {label: 'app.common.nav.home', to: {name: 'home'}, className: 'home'},
  {label: 'app.common.nav.service', to: {name: 'service'}, className: 'service'},
  {label: 'app.common.nav.fleet', to: {name: 'fleet'}, className: 'fleet'},
  {label: 'app.common.nav.about', to: {name: 'about'}, className: 'about'}
])

// 移动端菜单状态
const mobileMenuOpen = ref(false)
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const extraLinks = reactive([
  {id: 'safety', label: 'app.common.extraLinks.safety', to: {name: 'safety'}},
  {id: 'licenses', label: 'app.common.extraLinks.licenses', to: {name: 'licenses'}},
  {id: 'careers', label: 'app.common.extraLinks.careers', isOutOfLink: true, link: contactInfo.recruitUrl}
])
// 暗色模式
const isDarkMode = computed(() => currentTheme.value === 'dark')

// 语言列表
const langList = languageStore.langPackage
// 语言切换
const handleLanguageChange = (lang: string) => {
  setLocale(lang as AvailableLocales)
}

const handleContact = () => {
  router.push({name: 'contact'})
}

const handleClickExtraLink = (link: any) => {
  const routeData = router.resolve(link.to)
  if (typeof window !== 'undefined') {
    window.open(routeData.href, '_blank')
  }
}
</script>

<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
<style lang="scss">
.lang-option.active {
  color: #D11242;
}
</style>
