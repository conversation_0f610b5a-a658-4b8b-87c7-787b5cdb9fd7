import { ref, onMounted, watch } from 'vue'

// 主题类型
type Theme = 'light' | 'dark'

export function useTheme() {
  // 响应式主题状态
  const currentTheme = ref<Theme>('light')

  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') || 'light'
    currentTheme.value = savedTheme as Theme
    applyTheme(currentTheme.value)
  }

  // 应用主题到DOM
  const applyTheme = (theme: Theme) => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
      // document.body.classList.add('dark-theme')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 切换主题
  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', currentTheme.value)
    applyTheme(currentTheme.value)
    return currentTheme.value
  }

  // 设置特定主题
  const setTheme = (theme: Theme) => {
    currentTheme.value = theme
    localStorage.setItem('theme', theme)
    applyTheme(theme)
  }

  // 在组件挂载时初始化
  // onMounted(initTheme)

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
  })

  return {
    currentTheme,
    toggleTheme,
    setTheme,
    initTheme
  }
}
