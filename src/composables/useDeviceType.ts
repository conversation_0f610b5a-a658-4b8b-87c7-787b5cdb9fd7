import { ref, onMounted, onUnmounted } from 'vue'

export interface DeviceType {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}

export function useDeviceType() {
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isDesktop = ref(true)

  const checkDeviceType = () => {
    // 在SSR环境中，默认为桌面端
    if (typeof window === 'undefined') {
      isMobile.value = false
      isTablet.value = false
      isDesktop.value = true
      return
    }

    // Check for touch capability
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    // Get screen dimensions considering orientation
    const width = window.innerWidth
    const height = window.innerHeight

    // Check for mobile device characteristics
    const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

    // Determine device type based on multiple factors
    if (isMobileDevice && hasTouch) {
      if (width <= 768 || (width <= 1024 && height > width)) {
        isMobile.value = true
        isTablet.value = false
        isDesktop.value = false
      } else if (width <= 1024) {
        isMobile.value = false
        isTablet.value = true
        isDesktop.value = false
      } else {
        isMobile.value = false
        isTablet.value = false
        isDesktop.value = true
      }
    } else {
      isMobile.value = width <= 768
      isTablet.value = width > 768 && width <= 1024
      isDesktop.value = width > 1024
    }
  }

  // 添加防抖函数
  const debounce = <T extends (...args: any[]) => void>(fn: T, delay: number) => {
    let timer: number | undefined
    
    return function(this: any, ...args: Parameters<T>) {
      if (timer) window.clearTimeout(timer)
      timer = window.setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  // 使用防抖处理窗口调整事件
  const debouncedCheckDeviceType = debounce(checkDeviceType, 200)

  onMounted(() => {
    checkDeviceType() // 初始检查
    window.addEventListener('resize', debouncedCheckDeviceType)
    // 监听方向变化事件
    if (typeof window.orientation !== 'undefined') {
      window.addEventListener('orientationchange', checkDeviceType)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedCheckDeviceType)
    if (typeof window.orientation !== 'undefined') {
      window.removeEventListener('orientationchange', checkDeviceType)
    }
  })

  return {
    isMobile,
    isTablet,
    isDesktop,
    checkDeviceType
  }
}

// 导出常用的断点值
export const breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1024
} as const

// 导出媒体查询工具函数
export function getMediaQuery(breakpoint: number) {
  return `@media (max-width: ${breakpoint}px)`
} 