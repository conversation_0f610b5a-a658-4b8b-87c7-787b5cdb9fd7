import { useHead, useSeoMeta } from '@unhead/vue'

interface Breadcrumb {
  name: string
  path: string
}

interface SeoMetaOptions {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  locale?: string
  noIndex?: boolean
  canonicalUrl?: string
  ignoreAuth?: boolean
  breadcrumb?: Breadcrumb[]
}

export function useSeo() {
  const updateMeta = (options: SeoMetaOptions) => {
    const {
      title = 'Pan American Jet - Premium Private Aviation Services',
      description = 'Pan American Jet - Premium private aviation services worldwide. Charter flights, aircraft management, and luxury travel solutions.',
      keywords = 'private jet, charter flights, business aviation, luxury travel, aircraft management, Pan American Jet',
      image = 'https://www.panamericanjet.com/res/logo_whole.png',
      url = 'https://www.panamericanjet.com',
      type = 'website',
      author = 'Pan American Jet Corporation',
      publishedTime,
      modifiedTime,
      locale = 'en',
      noIndex = false,
      canonicalUrl
    } = options

    const meta = [
      { name: 'description', content: description },
      { name: 'keywords', content: keywords },
      { name: 'author', content: author },
      { name: 'robots', content: noIndex ? 'noindex, nofollow' : 'index, follow' },
      { name: 'locale', content: locale },
      
      // Open Graph
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:image', content: image },
      { property: 'og:url', content: canonicalUrl || url },
      { property: 'og:type', content: type },
      { property: 'og:locale', content: locale },
      
      // Twitter Card
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: title },
      { name: 'twitter:description', content: description },
      { name: 'twitter:image', content: image },
    ]

    // 添加可选的时间信息
    if (publishedTime) {
      meta.push({ property: 'article:published_time', content: publishedTime })
    }
    if (modifiedTime) {
      meta.push({ property: 'article:modified_time', content: modifiedTime })
    }

    useHead({
      title,
      meta,
      link: [
        { rel: 'canonical', href: canonicalUrl || url }
      ]
    })
  }

  return {
    updateMeta
  }
} 