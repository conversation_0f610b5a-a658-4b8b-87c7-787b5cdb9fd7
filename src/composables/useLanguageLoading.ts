import { ref } from 'vue'
import storage from '@/commons/storage'
import type { AvailableLocales } from '@/types/i18n.types'

export function useLanguageLoading() {
  const isLanguageReady = ref(false)

  // 确保语言包已加载
  const ensureLanguageLoaded = async () => {
    const currentLocale = storage.lang as AvailableLocales
    if (!currentLocale) return

    // 检查是否已有完整的语言包
    const langPackage = storage.langPackage
    if (langPackage?.[currentLocale]?.app) {
      isLanguageReady.value = true
      return
    }
    // 动态加载语言包
    try {
      if (window.__loadLanguage) {
        await window.__loadLanguage(currentLocale)
        isLanguageReady.value = true
      }
    } catch (error) {
      console.error('Failed to load language package:', error)
      // 如果加载失败，也设置为ready，至少显示基础翻译
      isLanguageReady.value = true
    }
  }

  return {
    isLanguageReady,
    ensureLanguageLoaded
  }
}
