import { onMounted, onUnmounted } from 'vue';

/**
 * A composable function that adds a CSS class to the header when a specific
 * target element is scrolled past.
 *
 * @param targetElementName The ID/Class of the element to observe. When this element's
 * top position is at or above the header's bottom position, the class is added.
 */
export function useHeaderScroll(targetElementName: string) {
  const headerSelector = '.c-header';
  const scrollClass = 'header-scrolled';

  const handleScroll = () => {
    const header = document.querySelector<HTMLElement>(headerSelector);
    const targetElement = document.querySelector(targetElementName);

    if (header && targetElement) {
      const targetPosition = targetElement.getBoundingClientRect().top;
      const headerHeight = header.offsetHeight;

      // Add class when the top of the target element is at or above the bottom of the sticky header.
      if (targetPosition <= headerHeight) {
        header.classList.add(scrollClass);
      } else {
        header.classList.remove(scrollClass);
      }
    }
  };

  onMounted(() => {
    window.addEventListener('scroll', handleScroll);
    // Run on mount to check initial position
    handleScroll();
  });

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
    // Clean up class on component unmount to avoid side effects on other pages
    const header = document.querySelector<HTMLElement>(headerSelector);
    if (header) {
      header.classList.remove(scrollClass);
    }
  });
} 