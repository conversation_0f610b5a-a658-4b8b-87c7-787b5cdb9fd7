import { defineStore } from 'pinia'
import type { Article, PaginatedResponse } from '@/types'

interface ArticleState {
  articles: Article[]
  currentArticle: Article | null
  loading: boolean
  error: string | null
  totalPages: number
  currentPage: number
}

export const useArticleStore = defineStore('article', {
  state: (): ArticleState => ({
    articles: [],
    currentArticle: null,
    loading: false,
    error: null,
    totalPages: 0,
    currentPage: 1
  }),
  
  getters: {
    featuredArticles: (state) => state.articles.filter(a => a.featured),
    
    articlesByTag: (state) => (tag: string) => 
      state.articles.filter(a => a.tags.includes(tag))
  },
  
  actions: {
    async fetchArticles(page = 1, limit = 10) {
      this.loading = true
      this.error = null
      
      try {
        // TODO: 实际项目中替换为真实 API 调用
        // const response = await api.getArticles(page, limit);
        
        // 模拟 API 响应
        const mockArticles: Article[] = Array(10).fill(null).map((_, index) => ({
          id: `${index + 1}`,
          title: `示例文章 ${index + 1}`,
          slug: `sample-article-${index + 1}`,
          content: '这是示例文章的内容...',
          summary: '这是一篇关于前端开发的示例文章。',
          tags: ['前端', 'Vue', 'JavaScript'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          author: {
            id: '1',
            name: '示例作者',
            email: '<EMAIL>',
            avatar: 'https://placeholder.pics/svg/100x100'
          },
          featured: index < 3 // 前三篇设为精选
        }))
        
        const mockResponse: PaginatedResponse<Article> = {
          data: mockArticles,
          totalItems: 30,
          totalPages: 3,
          currentPage: page,
          perPage: limit
        }
        
        this.articles = mockResponse.data
        this.totalPages = mockResponse.totalPages
        this.currentPage = mockResponse.currentPage
        
        return mockResponse
      } catch (error) {
        this.error = '获取文章列表失败'
        return null
      } finally {
        this.loading = false
      }
    },
    
    async fetchArticleBySlug(slug: string) {
      this.loading = true
      this.error = null
      
      try {
        // TODO: 实际项目中替换为真实 API 调用
        // const response = await api.getArticleBySlug(slug);
        
        // 模拟 API 响应
        const mockArticle: Article = {
          id: '1',
          title: '如何使用 Vue 3 Composition API',
          slug: slug,
          content: '# Vue 3 Composition API 入门\n\n这是一篇关于 Vue 3 Composition API 的详细教程...',
          summary: 'Vue 3 带来了全新的 Composition API，本文详细介绍其使用方法和优势。',
          coverImage: 'https://placeholder.pics/svg/800x400',
          tags: ['Vue', 'JavaScript', '前端'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          author: {
            id: '1',
            name: '示例作者',
            email: '<EMAIL>',
            avatar: 'https://placeholder.pics/svg/100x100'
          },
          featured: true
        }
        
        this.currentArticle = mockArticle
        return mockArticle
      } catch (error) {
        this.error = '获取文章详情失败'
        return null
      } finally {
        this.loading = false
      }
    }
  }
}) 