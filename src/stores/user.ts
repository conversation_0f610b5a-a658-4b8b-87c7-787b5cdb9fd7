import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm, UpdateProfileForm } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  // 登录状态
  const isLoggedIn = computed(() => !!token.value)

  // 用户信息
  const userProfile = computed(() => user.value)

  // 登录
  async function login(form: LoginForm) {
    try {
      // TODO: 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('登录失败')
      }

      const data = await response.json()
      token.value = data.token
      user.value = data.user
      localStorage.setItem('token', data.token)
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(error.message)
      }
      throw new Error('登录失败')
    }
  }

  // 注册
  async function register(form: RegisterForm) {
    try {
      // TODO: 调用注册API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('注册失败')
      }

      const data = await response.json()
      token.value = data.token
      user.value = data.user
      localStorage.setItem('token', data.token)
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(error.message)
      }
      throw new Error('注册失败')
    }
  }

  // 获取用户信息
  async function getProfile() {
    try {
      if (!token.value) {
        throw new Error('未登录')
      }

      // TODO: 调用获取用户信息API
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (!response.ok) {
        throw new Error('获取用户信息失败')
      }

      const data = await response.json()
      user.value = data
      return data
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(error.message)
      }
      throw new Error('获取用户信息失败')
    }
  }

  // 更新用户信息
  async function updateProfile(form: UpdateProfileForm) {
    try {
      if (!token.value) {
        throw new Error('未登录')
      }

      // TODO: 调用更新用户信息API
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token.value}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('更新用户信息失败')
      }

      const data = await response.json()
      user.value = data
      return data
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(error.message)
      }
      throw new Error('更新用户信息失败')
    }
  }

  // 登出
  function logout() {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
  }

  // 初始化
  function init() {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      getProfile().catch(() => {
        logout()
      })
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    userProfile,
    login,
    register,
    getProfile,
    updateProfile,
    logout,
    init
  }
}) 