<template lang="pug">
keep-alive
  router-view(v-if="route.meta.keepAlive")
router-view(v-if="!route.meta.keepAlive", :key="key")
</template>

<script setup lang="ts">
// App component
import {useLanguageStore} from '@/stores/language'
import {useRoute, useRouter} from 'vue-router'
import {computed, watch, nextTick} from 'vue'

const languageStore = useLanguageStore()
languageStore.initLangPackage()
const route = useRoute()
const router = useRouter()

const key = computed(() => {
  return (route.name?.toString() || 'default') + +new Date()
})

// 监听路由变化，每次跳转都滚动到顶部
watch(() => route.path, (newPath, oldPath) => {
  if (newPath !== oldPath) {
    nextTick(() => {
      // // 平滑滚动到页面顶部
      // window.scrollTo({
      //   top: 0,
      //   behavior: 'smooth'
      // })
      
      // 额外保险：如果平滑滚动不支持，直接跳转
      setTimeout(() => {
        if (window.scrollY > 0) {
          window.scrollTo(0, 0)
        }
      }, 100)
    })
  }
}, { flush: 'post' })
</script>

<style lang="scss">
@use "@/assets/scss/app.scss";
</style>
