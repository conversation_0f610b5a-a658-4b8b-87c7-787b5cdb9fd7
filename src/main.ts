import { ViteSSG } from 'vite-ssg'
import { createPinia } from 'pinia'
import App from './App.vue'
import { routes, setupRouterGuards } from './router'
import { i18n } from './i18n'
// import { useUserStore } from './stores/user'
import { useTheme } from './composables/useTheme'
import { setupIcons } from '@/plugins/icons'
import fontawesome from './plugins/fontawesome'

// https://github.com/antfu/vite-ssg
export const createApp = ViteSSG(
  App,
  {
    routes,
    base: import.meta.env.BASE_URL
  },
  ({ app, router, routes, isClient, initialState }) => {
    // 创建一个全局的 head 实例
    // const head = createHead({
    //   plugins: [PromisesPlugin]
    // })
    // app.use(head)

    const pinia = createPinia()
    app.use(pinia)
    app.use(i18n)
    app.use(fontawesome)
    // setupIcons(app)

    // 设置路由守卫
    setupRouterGuards(router)

    // // 初始化用户状态
    // const userStore = useUserStore()
    // userStore.init()

    // 初始化主题（必须在应用挂载后调用）
    if (isClient) {
      // 动态导入样式文件，避免 SSR 问题
      import('@/assets/css/main.css')
      import('@/assets/scss/main.scss')
      import('aos/dist/aos.css')
      import('@fortawesome/fontawesome-svg-core/styles.css')

      const { initTheme } = useTheme()
      initTheme()

      // 动态导入和初始化AOS动画
      import('aos').then((AOS) => {
        AOS.default.init({
          duration: 800,
          easing: 'ease-out-cubic',
          once: true,
          offset: 50,
          delay: 100
        })
      })

      // 控制初始可见性
      router.isReady().then(() => {
        setTimeout(() => {
          document.getElementById('app')?.classList.add('visible')
        }, 0)
      })
    }
  },
  {
    // SSG 配置选项
    rootContainer: '#app',
    transformState(state) {
      return JSON.stringify(state)
    }
  }
)

