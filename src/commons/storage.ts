import storage from 'store'
import expirePlugin from 'store/plugins/expire'

// 常量定义
const X_AUTH_TOKEN = 'PAJ_X_AUTH_TOKEN'
const USER_BASE_INFO = 'PAJ_LOGIN_INFO'
const ADMIN_INFO = 'PAJ_ACCOUNT'
const TRIP_REPLAY_INFO = 'TRIP_REPLAY_INFO'
const LANG = 'PAJ_LANG'
const LANG_PACKAGE = 'ADMIN_LANG_PACKAGE'
const SUPPORT_LANGUAGE = 'PAJ_SUPPORT_LANGUAGE'
const HTTP_STATUS = 'PAJ_HTTP_STATUS'
const BOM_HISTORY = 'PAJ_BOM_HISTORY'

// 添加插件
storage.addPlugin(expirePlugin)

// 定义类型接口
interface UserBaseInfo {
  userId?: string;
  username?: string;
  avatar?: string;
  [key: string]: any;
}

interface HttpStatusMessages {
  DEFAULT_ERROR: string;
  [key: string]: string;
}

interface LanguagePackage {
  [key: string]: any;
}

interface BomHistoryData {
  [key: string]: any;
}

// 存储类型
type StorageValueType = string | UserBaseInfo | HttpStatusMessages | LanguagePackage | BomHistoryData | null | undefined;

// 存储管理器
const storageManager = {
  // 系统当前语言
  get lang(): string | null {
    return storage.get(LANG) as string | null
  },
  set lang(val: string | null) {
    if (!val) {
      storage.remove(LANG)
    } else {
      storage.set(LANG, val)
    }
  },

  // 国际化语言包
  get langPackage(): LanguagePackage | null {
    return storage.get(LANG_PACKAGE) as LanguagePackage | null
  },
  set langPackage(val: LanguagePackage | null) {
    if (!val) {
      storage.remove(LANG_PACKAGE)
    } else {
      storage.set(LANG_PACKAGE, val)
    }
  },

  // http状态提示语
  get httpStatus(): HttpStatusMessages | null {
    return storage.get(HTTP_STATUS) as HttpStatusMessages | null
  },
  set httpStatus(val: HttpStatusMessages | null) {
    if (!val) {
      storage.remove(HTTP_STATUS)
    } else {
      storage.set(HTTP_STATUS, val)
    }
  },

  // 历史记录
  get bomHistory(): BomHistoryData | null {
    return storage.get(BOM_HISTORY) as BomHistoryData | null
  },
  set bomHistory(val: BomHistoryData | null) {
    if (!val) {
      storage.remove(BOM_HISTORY)
    } else {
      storage.set(BOM_HISTORY, val)
    }
  },

  // token
  get xAuthToken(): string | null {
    return storage.get(X_AUTH_TOKEN) as string | null
  },
  set xAuthToken(val: string | null) {
    if (!val) {
      storage.remove(X_AUTH_TOKEN)
    } else {
      storage.set(X_AUTH_TOKEN, val)
    }
  },

  // 用户基本信息
  get userBaseInfo(): UserBaseInfo | null {
    return storage.get(USER_BASE_INFO) as UserBaseInfo | null
  },
  set userBaseInfo(val: UserBaseInfo | null) {
    if (!val) {
      storage.remove(USER_BASE_INFO)
    } else {
      storage.set(USER_BASE_INFO, val)
    }
  },

  // 设置带过期时间的数据
  setWithExpiration(key: string, value: StorageValueType, expirationInSeconds: number): void {
    if (!value) {
      storage.remove(key)
    } else {
      // @ts-ignore - store插件的expire用法需要三个参数
      storage.set(key, value, new Date().getTime() + expirationInSeconds * 1000)
    }
  },

  // 清除所有存储
  clearAll(): void {
    storage.clearAll()
  },

  // 通用的getter和setter
  get(key: string): StorageValueType {
    return storage.get(key)
  },

  set(key: string, value: StorageValueType): void {
    if (!value) {
      storage.remove(key)
    } else {
      storage.set(key, value)
    }
  },

  remove(key: string): void {
    storage.remove(key)
  }
}

export default storageManager
