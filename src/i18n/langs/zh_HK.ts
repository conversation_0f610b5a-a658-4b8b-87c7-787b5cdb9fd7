import locale from 'element-plus/es/locale/lang/zh-hk'

// 默认错误消息
const DEFAULT_ERROR = '系统繁忙，请稍后再试'

export default {
  status: {
    DEFAULT_ERROR: DEFAULT_ERROR,
    404: '404 頁面不存在',
    500: DEFAULT_ERROR,
    700: DEFAULT_ERROR,
    1001: '服務接口不存在',
    1002: '沒有訪問權限，請聯絡管理員',
    1011: '賬號不存在，或賬號異常，請重新登入系統',
    1012: '賬號被禁用',
    1013: '用戶名或密碼與API網關不匹配',
    1101: '驗證碼創建失敗',
    1104: '圖片驗證碼錯誤',
    1103: '圖形驗證碼已使用',
    1105: '圖形驗證碼已過期'
  },
  ...locale
}
