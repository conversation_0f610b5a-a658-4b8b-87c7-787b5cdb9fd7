import locale from 'element-plus/es/locale/lang/zh-cn'

// 默认错误消息
const DEFAULT_ERROR = '系统繁忙，请稍后再试'

export default {
  status: {
    DEFAULT_ERROR: DEFAULT_ERROR,
    404: '404 页面不存在',
    500: DEFAULT_ERROR,
    700: DEFAULT_ERROR,
    1001: '服务接口不存在',
    1002: '没有访问权限，请联系管理员',
    1011: '账号不存在，或账号异常，请重新登陆系统',
    1012: '账号被禁用',
    1013: '用户名或密码与API网关不匹配',
    1101: '验证码创建失败',
    1104: '图片验证码错误',
    1103: '图形验证码已使用',
    1105: '图形验证码已过期'
  },
  ...locale
} 