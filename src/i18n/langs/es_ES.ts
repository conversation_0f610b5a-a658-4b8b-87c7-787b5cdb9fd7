import locale from 'element-plus/es/locale/lang/es'

// default error message
const DEFAULT_ERROR = 'El sistema esta ocupado. Por favor, inténtelo de nuevo más tarde.'

export default {
  status: {
    DEFAULT_ERROR: DEFAULT_ERROR,
    404: '404 no encontrado.',
    500: DEFAULT_ERROR,
    700: DEFAULT_ERROR,
    1001: 'Servicio API no existe',
    1002: 'No se permite acceso, contacte con el Administrador.',
    1011: 'Las cuentas no existen o no son normales.Por favor reingrese al sistema.',
    1012: 'Cuenta bloqueada',
    1013: 'El nombre de usuario o contraseña no coincide con el portal API',
    1101: 'Fallo al crear el Código',
    1104: 'Error del Código de autenticación',
    1103: 'El Código de autenticación gráfica ha sido usado.',
    1105: 'El Código gráfico ha caducado.'
  },
  ...locale
} 