import storage from '@/commons/storage'
import type {MessageSchema, AvailableLocales} from '@/types/i18n.types'
import {useLanguageStore} from '@/stores/language'
import {language, languageFilesVersion} from '@/conf/'
// 语言基础包
import zh_CN from './zh_CN'
import zh_HK from './zh_HK'
import en_US from './en_US'
// 导入其他语言包
import es_ES_raw from './es_ES'
import pt_PT_raw from './pt_PT'
import ar_AE_raw from './ar_AE'

// 直接导入JSON翻译文件（用于SSR环境）
import en_US_json from '../../../public/data/i18n/en_US.json'
import zh_CN_json from '../../../public/data/i18n/zh_CN.json'
import zh_HK_json from '../../../public/data/i18n/zh_HK.json'
import es_ES_json from '../../../public/data/i18n/es_ES.json'
import pt_PT_json from '../../../public/data/i18n/pt_PT.json'
import ar_AE_json from '../../../public/data/i18n/ar_AE.json'

// 类型转换
const es_ES = es_ES_raw as unknown as MessageSchema
const pt_PT = pt_PT_raw as unknown as MessageSchema
const ar_AE = ar_AE_raw as unknown as MessageSchema

// 合并基础语言包和JSON翻译数据
const mergeMessages = (base: MessageSchema, jsonData: any): MessageSchema => {
  return {
    ...base,
    app: jsonData
  }
}

// 检查是否在SSR/SSG环境中
const isSSR = typeof window === 'undefined'

// 基础语言包（不包含app翻译）
const BASE_MESSAGES: Record<AvailableLocales, MessageSchema> = {
  zh_CN: zh_CN,
  zh_HK: zh_HK,
  en_US: en_US,
  es_ES: es_ES,
  pt_PT: pt_PT,
  ar_AE: ar_AE
}

// 完整翻译消息（包含JSON翻译数据）
const COMPLETE_MESSAGES: Record<AvailableLocales, MessageSchema> = {
  zh_CN: mergeMessages(zh_CN, zh_CN_json),
  zh_HK: mergeMessages(zh_HK, zh_HK_json),
  en_US: mergeMessages(en_US, en_US_json),
  es_ES: mergeMessages(es_ES, es_ES_json),
  pt_PT: mergeMessages(pt_PT, pt_PT_json),
  ar_AE: mergeMessages(ar_AE, ar_AE_json)
}

// 初始化语言包集合
let MESSAGES: Record<AvailableLocales, MessageSchema>

if (isSSR) {
  // SSR/SSG环境：使用完整翻译数据确保预渲染正确
  MESSAGES = COMPLETE_MESSAGES
  // console.log('SSR environment: using complete translation messages for proper pre-rendering')
} else {
  // 浏览器环境：只包含基础语言包，app部分将动态加载
  MESSAGES = BASE_MESSAGES
  // console.log('Browser environment: using base messages, app translations will be loaded dynamically')
}

const DEFAULT_LOCALE: AvailableLocales = language.default as AvailableLocales

// 在客户端环境下，动态加载翻译文件（优化性能）
if (!isSSR) {
  const locale = storage.lang as AvailableLocales || DEFAULT_LOCALE

  // 动态获取语言文件URL
  const getExtendJsonUrl = (locale: AvailableLocales) => `/data/i18n/${locale}.json?t=${languageFilesVersion}`

  // 动态加载指定语言的翻译文件
  const loadLanguageData = async (targetLocale: AvailableLocales) => {
    try {
      const response = await fetch(getExtendJsonUrl(targetLocale))
      const extendJson = await response.json()

      // 更新指定语言的翻译数据
      if (MESSAGES[targetLocale]) {
        MESSAGES[targetLocale] = mergeMessages(BASE_MESSAGES[targetLocale], extendJson)
        storage.langPackage = MESSAGES
      }
      storage.lang = locale
      console.log(`Dynamically loaded translations for ${targetLocale}`)
      return true
    } catch (e) {
      console.warn(`Failed to load extended i18n data for ${targetLocale}`, e)
      return false
    }
  }

  // 加载当前语言的翻译数据
  loadLanguageData(locale).finally(() => {
    const languageStore = useLanguageStore()
    languageStore.setLang(locale)
  })

  // 导出动态加载函数供语言切换时使用
  // 这样在切换语言时只加载需要的语言包，优化性能
  ;(window as any).__loadLanguage = loadLanguageData
}

// 将语言配置包存储到本地，方便其他组件调用
storage.langPackage = MESSAGES
// 储存默认http状态提示语
storage.httpStatus = MESSAGES[DEFAULT_LOCALE]?.status || MESSAGES.en_US.status

export default MESSAGES
