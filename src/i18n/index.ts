import {createI18n, useI18n} from 'vue-i18n'
import storage from '@/commons/storage'
import messages from './langs/index'
import type {MessageSchema, AvailableLocales} from '@/types/i18n.types'
import type {ConfigProviderProps} from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import zhHK from 'element-plus/es/locale/lang/zh-hk'
import en from 'element-plus/es/locale/lang/en'
import es from 'element-plus/es/locale/lang/es'
import ar from 'element-plus/es/locale/lang/ar'
import pt from 'element-plus/es/locale/lang/pt'
import {language, languageFilesVersion} from '@/conf'
import type {ExtendedRequestConfig} from '@/utils/request'
import http from '@/utils/request'
import {ElLoading, ElMessage} from 'element-plus'
import {useLanguageStore} from '@/stores/language'

// 默认语言设置
const DEFAULT_LOCALE: AvailableLocales = language.default as AvailableLocales

// Element Plus 语言配置对应
const elementLocales: Record<string, ConfigProviderProps['locale']> = {
  zh_CN: zhCn,
  zh_HK: zhHK,
  en_US: en,
  es_ES: es,
  ar_AE: ar,
  pt_PT: pt
}

// 从localStorage获取用户语言选择，如果没有，则使用默认语言
const locale = typeof window !== 'undefined' ? ((storage.lang as AvailableLocales) || DEFAULT_LOCALE) : DEFAULT_LOCALE
// 创建i18n实例
export const i18n = createI18n<[MessageSchema], AvailableLocales>({
  legacy: false,
  locale: locale,
  fallbackLocale: language.default,
  messages: messages
})

// 设置语言
export function setLocale (newLocale: AvailableLocales) {
  const THIS_MESSAGES = (storage.langPackage && storage.langPackage[newLocale]) || {}

  if (THIS_MESSAGES.app) {
    setLocaleData(newLocale, THIS_MESSAGES.app)
    return false
  }
  http({
    method: 'get',
    mask: true,
    apiType: 'LOCAL',
    url: 'data/i18n/' + language.data[newLocale] + '?t=' + languageFilesVersion
  } as ExtendedRequestConfig)
    .then((res: any) => {
      setLocaleData(newLocale, res)
    })
    .catch((errCatch: any) => {
      ElMessage.error(i18n.global.t('app.languageChangeTipError'))
    })
  // if (i18n.global.locale) {
  //   // @ts-ignore - vue-i18n typing issue
  //   i18n.global.locale.value = newLocale
  // }
}

export const setLocaleData = async (_lang: any, _messages: any) => {
  const languageStore = useLanguageStore()
  const THIS_MESSAGES = i18n.global.getLocaleMessage(_lang)
  const storageLangPackage = storage.langPackage || {}
  if (i18n.global.locale) {
    // @ts-ignore - vue-i18n typing issue
    i18n.global.locale.value = _lang
  }
  THIS_MESSAGES.app = _messages
  storageLangPackage[_lang].app = _messages
  storage.langPackage = storageLangPackage
  // storage.httpStatus = i18n.global.t('status')
  i18n.global.setLocaleMessage(_lang, THIS_MESSAGES)
  document.querySelector('html')?.setAttribute('lang', _lang)
  languageStore.setLang(_lang)
  // setDocumentTitle(vm)
}
export const languageMap = {
  en_US: {
    short: 'en',
    num: 1,
    name: 'English'
  },
  es_ES: {
    short: 'es',
    num: 2,
    name: 'Español'
  },
  pt_PT: {
    short: 'po',
    num: 3,
    name: 'Português'
  },
  ar_AE: {
    short: 'ar',
    num: 5,
    name: 'عربي'
  }
}

export function setDocumentTitle (vm: any) {
  // const docTitleState = vm.$store.state.common.documentTitle
  const docTitleState = vm
  let value = ''
  if (docTitleState && docTitleState.docTitleI18n) {
    const docTitleDefault = `app.common.documentTitle.${docTitleState.docTitleI18nModule}.default`
    const docTitleI18n = `app.common.documentTitle.${docTitleState.docTitleI18nModule}.${docTitleState.docTitleI18n}`
    value = i18n.global.t(docTitleI18n)
    if (value === docTitleI18n) value = i18n.global.t(docTitleDefault)
    if (value === docTitleDefault) value = ''
  }
  document.title = value || docTitleState.title || docTitleState.default
}

// 获取当前Element Plus语言配置
// export function getElementLocale() {
//   const currentLocale = (localStorage.getItem('locale') as AvailableLocales) || DEFAULT_LOCALE
//   return elementLocales[currentLocale] || elementLocales[DEFAULT_LOCALE]
// }

export default i18n
