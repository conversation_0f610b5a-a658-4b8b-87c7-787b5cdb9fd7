// main.scss - 全局样式
@use "@/assets/scss/element/element-functions.scss";

// 公共样式
body {
  font-family: "Noto Sans SC", "Noto Sans CJK", "Source Han Sans", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: color 0.3s, background-color 0.3s;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

// 全局过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-menu-enter-active,
.slide-menu-leave-active {
  transition: all 0.3s ease;
}

.slide-menu-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-menu-enter-to {
  transform: translateX(0);
  opacity: 1;
}

.slide-menu-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.slide-menu-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

button {
  font-family: "Noto Sans SC", "Noto Sans CJK", "Source Han Sans", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}
