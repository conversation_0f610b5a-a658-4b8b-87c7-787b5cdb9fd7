// 颜色变量
$primary-color: #CC7566;
$secondary-color: #64748b;
$text-color: #1f2937;
$bg-color: #ffffff;
$border-color: #e5e7eb;

// 字体和尺寸
$font-family: 'Inter', sans-serif;
$border-radius: 4px;

// 间距变量
$spacing-base: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 断点变量
$breakpoints: (
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px,
);

// 媒体查询混合宏
@mixin respond-to($breakpoint) {
  $value: map-get($breakpoints, $breakpoint);

  @if $value != null {
    @media (min-width: $value) {
      @content;
    }
  }
}
