import { get, post } from '@/utils/request'

// 定义接口返回类型
export interface LoginResponse {
  code: number
  message: string
  data: {
    token: string
    userId: string
    userName: string
  }
}

export interface UserInfo {
  userId: string
  userName: string
  avatar: string
  email: string
  role: string[]
  permissions: string[]
}

/**
 * 用户登录
 * @param username 用户名
 * @param password 密码
 * @returns 登录结果
 */
export function login(username: string, password: string): Promise<LoginResponse> {
  return post<LoginResponse>('/user/login', {
    username,
    password
  })
}

/**
 * 获取用户信息
 * @returns 用户信息
 */
export function getUserInfo(): Promise<UserInfo> {
  return get<UserInfo>('/user/info')
}

/**
 * 退出登录
 */
export function logout(): Promise<any> {
  return post('/user/logout')
}

/**
 * 注册用户
 * @param userData 用户数据
 */
export function register(userData: {
  username: string
  password: string
  email: string
}): Promise<any> {
  return post('/user/register', userData)
}

/**
 * 修改密码
 * @param data 密码数据
 */
export function changePassword(data: {
  oldPassword: string
  newPassword: string
}): Promise<any> {
  return post('/user/change-password', data)
} 