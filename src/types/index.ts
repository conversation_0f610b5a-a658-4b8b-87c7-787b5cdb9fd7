export interface User {
  id: string
  name: string
  email: string
  avatar?: string
}

export interface Article {
  id: string
  title: string
  slug: string
  content: string
  summary: string
  coverImage?: string
  tags: string[]
  createdAt: string
  updatedAt: string
  author: User
  featured?: boolean
}

export interface Comment {
  id: string
  content: string
  createdAt: string
  author: User
  articleId: string
  parentId?: string
}

export interface ApiResponse<T> {
  data: T
  message?: string
  status: number
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  totalItems: number
  totalPages: number
  currentPage: number
  perPage: number
} 