import storage from '@/commons/storage'

let shortUrlDefault = 'https://s.szdev.fleetup.cc'
if (typeof window !== 'undefined' && /(online\.|staging\.)/.test(window.location.host)) {
  shortUrlDefault = 'https://eta.fleetup.cc'
} else {
  shortUrlDefault = 'https://s.szdev.fleetup.cc'
}

const getOrigin = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin
  }
  return 'https://www.panamericanjet.com'
}

export const sApis = {
  ENDPOINTDEFAULT:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      storage.userBaseInfo.endpoints.default) ||
    getOrigin(),
  ADMIN:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      (storage.userBaseInfo.endpoints.admin || storage.userBaseInfo.endpoints.default)) ||
    getOrigin(),
  SHIPMENT:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      (storage.userBaseInfo.endpoints.shipment || storage.userBaseInfo.endpoints.default)) ||
    getOrigin(),
  SHORTURL:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      storage.userBaseInfo.endpoints.shortLink) ||
    shortUrlDefault
}
// http接口地址（类型）根路径
export const API = {
  // 开发环境
  development: {
    DEFAULT: '/api',
    NONE: '',
    LOCAL: import.meta.env.BASE_URL,
    ADMIN: sApis.ADMIN,
    ENDPOINTDEFAULT: sApis.ENDPOINTDEFAULT,
    SHIPMENT: sApis.SHIPMENT,
    CDN: 'https://cdn.fleetup.net',
    BAYANAT: 'https://maps.bayanat.co.ae',
    SHORTURL: '/szdev'
  },
  // 生产环境
  production: {
    DEFAULT: getOrigin(),
    LOCAL: import.meta.env.BASE_URL,
    ADMIN: sApis.ADMIN,
    ENDPOINTDEFAULT: sApis.ENDPOINTDEFAULT,
    SHIPMENT: sApis.SHIPMENT, // test5.sz.fleetup.net：sApis.SHIPMENT, s3: window.location.origin
    CDN: 'https://cdn.fleetup.net',
    BAYANAT: 'https://maps.bayanat.co.ae',
    SHORTURL: sApis.SHORTURL
  }
}

// 项目语言配置
export const language: Record<string, any> = {
  // 默认语言
  default: 'en_US',
  // 支持语言 '1': 'zh_CN', '2': 'en_US', '3': 'es_ES', '4': 'pt_PT', '5': 'ar_AE', '6': 'zh_HK'
  package: {'1': 'zh_CN', '6': 'zh_HK', '2': 'en_US'},
  order: {
    'en_US': 1,
    'zh_CN': 2,
    'zh_HK': 3
  },
  // 语言别名
  alias: {
    zh_CN: '中文简体',
    zh_HK: '中文繁体',
    ar_AE: 'عربي',
    de_DE: 'Dansk',
    en_US: 'English',
    es_ES: 'Español',
    fr_FR: 'Français',
    ja_JP: '日本語',
    ko_KR: '대한민국 - 한국어',
    ru_RU: 'русский язык',
    pt_PT: 'Português'
  },
  // 语言包图标
  icons: {
    zh_CN: '/res/zh_CN.png',
    zh_HK: '/res/zh_HK.png',
    ar_AE: '/res/ar_AE.png',
    de_DE: '/res/de_DE.png',
    en_US: '/res/en_US.png',
    es_ES: '/res/es_ES.png',
    fr_FR: '/res/fr_FR.png',
    ja_JP: '/res/ja_JP.png',
    ko_KR: '/res/ko_KR.png',
    ru_RU: '/res/ru_RU.png',
    pt_PT: '/res/pt_PT.png'
  },
  // 语言包数据
  data: {
    zh_CN: 'zh_CN.json',
    zh_HK: 'zh_HK.json',
    ar_AE: 'ar_AE.json',
    de_DE: 'de_DE.json',
    en_US: 'en_US.json',
    es_ES: 'es_ES.json',
    fr_FR: 'fr_FR.json',
    ja_JP: 'ja_JP.json',
    ko_KR: 'ko_KR.json',
    ru_RU: 'ru_RU.json',
    pt_PT: 'pt_PT.json'
  }
}

//用户联系信息
export const contactInfo: Record<string, any> = {
  careersEmail: '<EMAIL>',
  salesEmail: '<EMAIL>',
  address: 'app.common.footer.address',
  salesPhone: '13424389902',
  salesPhoneCountryCode: '+86',
  recruitUrl: 'https://job.carnoc.com/company?id=44ef8754c5ab6d23bdd190c069495d95'
}

export const languageFilesVersion = '1750689216508'
