import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Services = () => import('@/views/service/index.vue')
const ServicesDetail = () => import('@/views/service/detail/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/service',
    component: DefaultLayout,
    meta: {
      title: 'Business Aviation Services - Pan American Jet',
      description: 'Discover our comprehensive range of business aviation services including charter flights, aircraft management, maintenance, and concierge services. Experience the highest standards of luxury and safety.',
      keywords: 'private jet services, aircraft charter, jet management, aviation maintenance, concierge service, luxury travel solutions',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      image: '/res/logo_whole.png',
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'Services', path: '/service' }
      ]
    },
    children: [
      {
        path: '',
        name: 'service',
        component: Services,
        meta: {
          ignoreAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'serviceDetail',
        component: ServicesDetail,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
