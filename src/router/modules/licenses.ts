import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Licenses = () => import('@/views/licenses/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/licenses',
    component: DefaultLayout,
    meta: {
      title: 'Licensed - Pan American Jet',
      description: 'Pan American Jet (泛美公务航空) is a licensed business aviation provider registered in Hong Kong, offering certified private jet charter, leasing, and aircraft management services./7 to assist with charter flights, inquiries, and personalized services. Reach out today.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      image: '/res/logo_whole.png',
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'Licenses', path: '/licenses' }
      ]
    },
    children: [
      {
        path: '',
        name: 'licenses',
        component: Licenses,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
