import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Home = () => import('@/views/home/<USER>')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/',
    component: DefaultLayout,
    meta: {
      title: 'Pan American Jet - Leading Business Aviation Services',
      description: 'Experience unparalleled luxury in business aviation with Pan American Jet (泛美公务航空). We offer premium charter flights, aircraft management, and personalized travel solutions worldwide.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      ogImage: '/res/logo_whole.png',
      locale: 'en',
      noIndex: false,
      image: '/res/logo_whole.png',
      breadcrumb: [
        { name: 'Home', path: '/' }
      ]
    },
    children: [
      {
        path: '',
        name: 'home',
        component: Home,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
