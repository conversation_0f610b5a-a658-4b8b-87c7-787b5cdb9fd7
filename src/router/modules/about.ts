import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const About = () => import('@/views/about/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/about',
    component: DefaultLayout,
    meta: {
      title: 'About Us - Pan American Jet',
      description: 'Learn about Pan American Jet\'s history(泛美公务航空), mission, and commitment to excellence in private aviation. Discover our values and what makes us the leading choice for private jet services.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'About Us', path: '/about' }
      ]
    },
    children: [
      {
        path: '',
        name: 'about',
        component: About,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
