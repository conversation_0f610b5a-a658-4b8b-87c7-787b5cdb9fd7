import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Contact = () => import('@/views/contact/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/contact',
    component: DefaultLayout,
    meta: {
      title: 'Contact Pan American Jet - Get in Touch',
      description: 'Contact Pan American Jet (泛美公务航空) for all your private aviation needs. Our dedicated team is available 24/7 to assist with charter flights, inquiries, and personalized services. Reach out today.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      image: '/res/logo_whole.png',
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'Contact', path: '/contact' }
      ]
    },
    children: [
      {
        path: '',
        name: 'contact',
        component: Contact,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
