import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Safety = () => import('@/views/safety/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/safety',
    component: DefaultLayout,
    meta: {
      title: 'Safety - Pan American Jet',
      description: 'Committed to the highest safety standards, Pan American Jet (泛美公务航空) complies with IS-BAO and operates with certified aircraft, experienced pilots, and rigorous maintenance protocols.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      image: '/res/logo_whole.png',
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'Safety', path: '/safety' }
      ]
    },
    children: [
      {
        path: '',
        name: 'safety',
        component: Safety,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
