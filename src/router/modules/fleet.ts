import type { RouteRecordRaw } from 'vue-router'

const DefaultLayout = () => import('@/views/layouts/index.vue')
const Fleet = () => import('@/views/fleet/index.vue')
const FleetDetail = () => import('@/views/fleet/detail/index.vue')

export default function(routes: RouteRecordRaw[]) {
  routes.push({
    path: '/fleet',
    component: DefaultLayout,
    meta: {
      title: 'Our Fleet - Pan American Jet',
      description: 'Explore Pan American Jet (泛美公务航空) diverse fleet of private jets available for charter. From light jets to heavy jets, we offer the perfect aircraft for your needs.',
      type: 'website',
      author: 'Pan American Jet Corporation',
      locale: 'en',
      noIndex: false,
      breadcrumb: [
        { name: 'Home', path: '/' },
        { name: 'Fleet', path: '/fleet' }
      ]
    },
    children: [
      {
        path: '',
        name: 'fleet',
        component: Fleet,
        meta: {
          ignoreAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'fleetDetail',
        component: FleetDetail,
        meta: {
          ignoreAuth: true
        }
      }
    ]
  })
}
