import { createRouter, createWebHistory, createMemoryHistory } from 'vue-router'
import type { RouteRecordRaw, Router } from 'vue-router'
import { useSeo } from '@/composables/useSeo'

// 导入所有路由模块
const modules = import.meta.glob('./modules/*.ts', { eager: true })

// 导出路由配置数组
export const routes: RouteRecordRaw[] = []

// 遍历模块并注册路由
Object.values(modules).forEach((module: any) => {
  if (module.default) {
    module.default(routes)
  }
})

// 创建路由实例
const router = createRouter({
  history: typeof window !== 'undefined'
    ? createWebHistory(import.meta.env.BASE_URL)
    : createMemoryHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // ViteSSG环境下，scrollBehavior可能不生效，我们在App.vue中用watch处理
    // 这里保留配置以防万一某些情况下生效
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    
    return { top: 0, behavior: 'smooth' }
  }
})

// 合并父子路由的 meta 信息
function getMergedMeta(to: any) {
  let merged = { ...to.meta }

  // 如果有父路由，合并父路由的 meta
  if (to.matched.length > 1) {
    const parentMeta = to.matched[0].meta
    merged = {
      ...parentMeta,  // 父路由的 meta 作为基础
      ...to.meta,     // 子路由的 meta 可以覆盖父路由
      // 特殊处理面包屑，如果子路由有自己的面包屑，使用子路由的
      breadcrumb: to.meta.breadcrumb || parentMeta.breadcrumb
    }
  }

  return merged
}

// 导出路由守卫配置函数
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 获取合并后的 meta 信息
    const mergedMeta = getMergedMeta(to)

    // 更新 SEO meta 标签
    const { updateMeta } = useSeo()
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://www.panamericanjet.com'

    try {
      await updateMeta({
        title: mergedMeta.title as string,
        description: mergedMeta.description as string,
        keywords: mergedMeta.keywords as string,
        type: mergedMeta.type as string,
        author: mergedMeta.author as string,
        locale: mergedMeta.locale as string,
        noIndex: mergedMeta.noIndex as boolean,
        url: `${origin}${to.fullPath}`,
        breadcrumb: mergedMeta.breadcrumb
      })
    } catch (error) {
      console.error('Failed to update meta tags:', error)
    }

    // 设置文档标题
    // document.title = to.meta.title
    //   ? `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE || 'Tech Blog'}`
    //   : import.meta.env.VITE_APP_TITLE || 'Tech Blog'
    // 认证检查
    // const isLoggedIn = !!localStorage.getItem('token')

    // // 需要认证的路由
    // if (!to.meta.ignoreAuth && !isLoggedIn) {
    //   next({ name: 'login', query: { redirect: to.fullPath } })
    // }
    // // 仅游客可访问的路由
    // else if (to.meta.guest && isLoggedIn) {
    //   next({ name: 'home' })
    // }
    // else {
    //   next()
    // }
    next()
  })
}

// 导出路由实例
export default router
