/**
 * CDN基础URL
 * 如果环境变量中没有配置，则使用相对路径
 */
const CDN_BASE_URL = import.meta.env.VITE_APP_CDN_URL || ''

/**
 * 获取资源的CDN URL
 * @param path 资源路径
 * @returns 完整的CDN URL
 */
export function getCdnUrl(path: string): string {
  // 如果是完整的URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }
  
  // 如果是相对路径，拼接CDN基础URL
  return `${CDN_BASE_URL}${path.startsWith('/') ? '' : '/'}${path}`
}

/**
 * 获取图片的CDN URL，并添加图片处理参数
 * @param path 图片路径
 * @param options 图片处理选项
 * @returns 处理后的图片URL
 */
export function getImageUrl(path: string, options?: {
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'jpeg' | 'png'
}): string {
  const baseUrl = getCdnUrl(path)
  
  // 如果没有CDN配置或没有处理选项，直接返回基础URL
  if (!CDN_BASE_URL || !options) {
    return baseUrl
  }
  
  // 构建查询参数
  const params = new URLSearchParams()
  
  if (options.width) {
    params.append('w', options.width.toString())
  }
  
  if (options.height) {
    params.append('h', options.height.toString())
  }
  
  if (options.quality) {
    params.append('q', options.quality.toString())
  }
  
  if (options.format) {
    params.append('fmt', options.format)
  }
  
  const queryString = params.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

/**
 * 预加载图片
 * @param urls 图片URL数组
 */
export function preloadImages(urls: string[]): void {
  urls.forEach(url => {
    const img = new Image()
    img.src = getCdnUrl(url)
  })
} 