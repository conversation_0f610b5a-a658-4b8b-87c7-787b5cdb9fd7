/**
 * 生成低质量的图片预览URL
 * @param imageUrl 原始图片URL
 * @returns Promise<string> 低质量图片的base64 URL
 */
export async function generateLowQualityPreview(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      // 创建一个小尺寸的canvas
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      // 设置较小的尺寸
      const MAX_WIDTH = 50
      const scale = MAX_WIDTH / img.width
      canvas.width = MAX_WIDTH
      canvas.height = img.height * scale
      
      if (ctx) {
        // 使用低质量绘制
        ctx.imageSmoothingQuality = 'low'
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        // 转换为base64，使用较低的质量
        const blurredBase64 = canvas.toDataURL('image/jpeg', 0.1)
        resolve(blurredBase64)
      } else {
        reject(new Error('Failed to get canvas context'))
      }
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    img.src = imageUrl
  })
}

/**
 * 预加载图片
 * @param imageUrl 图片URL
 * @returns Promise<void>
 */
export function preloadImage(imageUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = () => reject(new Error(`Failed to preload image: ${imageUrl}`))
    img.src = imageUrl
  })
}

/**
 * 批量预加载图片
 * @param imageUrls 图片URL数组
 * @returns Promise<void[]>
 */
export function preloadImages(imageUrls: string[]): Promise<void[]> {
  return Promise.all(imageUrls.map(url => preloadImage(url)))
} 