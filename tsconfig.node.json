{"extends": "@tsconfig/node22/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*"], "compilerOptions": {"composite": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "NodeNext", "moduleResolution": "NodeNext", "types": ["node"], "target": "ESNext", "allowSyntheticDefaultImports": true}}