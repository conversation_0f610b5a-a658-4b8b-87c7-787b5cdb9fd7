import {fileURLToPath, URL} from 'node:url'
import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import tailwind from '@tailwindcss/vite'
import postcssPlugin from '@tailwindcss/postcss'
import autoprefixer from 'autoprefixer'
import i18nMergePlugin from './plugins/vite-i18n-plugin'
import {createHtmlPlugin} from 'vite-plugin-html'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

// https://vite.dev/config/
export default defineConfig(({mode}) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  return {
    base: '/',
    plugins: [
      i18nMergePlugin(),
      vue({
        template: {
          compilerOptions: {
            // 处理 Tailwind CSS 中的特殊字符在 pug 中的兼容性问题
            isCustomElement: (tag) => tag.includes('_')
          }
        }
      }),
      vueJsx(),
      vueDevTools(),
      Icons({
        autoInstall: true,
        compiler: 'vue3'
      }),
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto import icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ]
      }),
      Components({
        dirs: [
          'src'
        ],
        extensions: ['vue'],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto register icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ],
        deep: true,
        directoryAsNamespace: true
      }),
      tailwind(),
      createHtmlPlugin({
        minify: true,
        inject: {
          data: {
            TITLE: env.VITE_APP_TITLE || 'Tech Blog'
          }
        }
      })
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/assets/scss/variables.scss" as *;
            @use "@/assets/scss/element/index.scss" as *;
          `
        }
      },
      postcss: {
        plugins: [
          postcssPlugin(),
          autoprefixer()
        ]
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@res': fileURLToPath(new URL('public/res', import.meta.url)),
        '@data': fileURLToPath(new URL('public/data', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0',
      port: 8916,
      proxy: {
        '/api': {
          target: 'https://online.dev.fleetup.net/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/trunk': {
          target: 'https://trunk.fleetup.net',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/szdev': {
          target: 'https://s.szdev.fleetup.cc',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/szdev/, '')
        }
      }
    },
    build: {
      outDir: './dist',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.time', 'console.timeEnd']
        }
      },
      rollupOptions: {
        input: {
          main: fileURLToPath(new URL('./index.html', import.meta.url))
        },
        external: [
          'vue',
          'vue-router',
          'pinia'
          // 在这里添加不想打包的外部依赖
        ],
        output: {
          // 全局变量名称映射，相当于webpack的externals中的globals配置
          globals: {},
          // JS文件输出配置
          chunkFileNames: (chunkInfo) => {
            return 'static/js/[name]-[hash].js'
          },
          entryFileNames: 'static/js/[name]-[hash].js',
          // 资源文件输出配置
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') ?? []
            const ext = info[info.length - 1]
            
            // CSS 文件
            if (/\.(css)$/i.test(assetInfo.name ?? '')) {
              return 'static/css/[name]-[hash].[ext]'
            }
            
            // 图片文件
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name ?? '')) {
              return 'static/imgs/[name]-[hash].[ext]'
            }
            
            // 字体文件
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name ?? '')) {
              return 'static/fonts/[name]-[hash].[ext]'
            }
            
            // 其他资源文件
            return 'static/[name]-[hash].[ext]'
          }
        }
      }
    },
    assetsInclude: ['**/*.jpg', '**/*.png', '**/*.svg', '**/*.webp']
  }
})
