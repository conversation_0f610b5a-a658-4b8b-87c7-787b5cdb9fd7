/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    App: typeof import('./src/App.vue')['default']
    ComponentsCFooter: typeof import('./src/components/c-footer/index.vue')['default']
    ComponentsCHeader: typeof import('./src/components/c-header/index.vue')['default']
    ComponentsCPicture: typeof import('./src/components/c-picture/index.vue')['default']
    ComponentsIconsIconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    ComponentsIconsIconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    ComponentsIconsIconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    ComponentsIconsIconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    ComponentsIconsIconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    ComponentsImageLoader: typeof import('./src/components/ImageLoader.vue')['default']
    ComponentsLanguageReady: typeof import('./src/components/language-ready/index.vue')['default']
    ComponentsLoadingAnimation: typeof import('./src/components/loading-animation/index.vue')['default']
    ComponentsStructuredData: typeof import('./src/components/StructuredData.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRow: typeof import('element-plus/es')['ElRow']
    IEpArrowRight: typeof import('~icons/ep/arrow-right')['default']
    IEpCheck: typeof import('~icons/ep/check')['default']
    IEpCloseBold: typeof import('~icons/ep/close-bold')['default']
    IEpDocumentCopy: typeof import('~icons/ep/document-copy')['default']
    IEpMessage: typeof import('~icons/ep/message')['default']
    IEpPhoneFilled: typeof import('~icons/ep/phone-filled')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ViewsAbout: typeof import('./src/views/about/index.vue')['default']
    ViewsContact: typeof import('./src/views/contact/index.vue')['default']
    ViewsFleet: typeof import('./src/views/fleet/index.vue')['default']
    ViewsFleetDetail: typeof import('./src/views/fleet/detail/index.vue')['default']
    ViewsHome: typeof import('./src/views/home/<USER>')['default']
    ViewsLayouts: typeof import('./src/views/layouts/index.vue')['default']
    ViewsLicenses: typeof import('./src/views/licenses/index.vue')['default']
    ViewsSafety: typeof import('./src/views/safety/index.vue')['default']
    ViewsService: typeof import('./src/views/service/index.vue')['default']
    ViewsServiceDetail: typeof import('./src/views/service/detail/index.vue')['default']
  }
}
