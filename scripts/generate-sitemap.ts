import {writeFileSync, readFileSync, existsSync} from 'fs'
import {resolve} from 'path'
import {fileURLToPath} from 'url'
import {dirname} from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 配置你的网站 URL
const BASE_URL = 'https://www.panamericanjet.com'

// 时间戳文件路径
const TIMESTAMPS_FILE = resolve(__dirname, './page-timestamps.json')

// 读取时间戳
function getTimestamps (): Record<string, string> {
  if (existsSync(TIMESTAMPS_FILE)) {
    try {
      return JSON.parse(readFileSync(TIMESTAMPS_FILE, 'utf-8'))
    } catch (error) {
      console.warn('Failed to parse timestamps file:', error)
      return {}
    }
  }
  return {}
}

// 保存时间戳
function saveTimestamps (timestamps: Record<string, string>) {
  writeFileSync(TIMESTAMPS_FILE, JSON.stringify(timestamps, null, 2))
}

// 语言配置
const LANGUAGES = {
  en: {
    path: '',  // 英文版本使用根路径
    name: 'English'
  },
  zh: {
    path: '/zh',  // 中文版本使用 /zh 路径
    name: '中文'
  }
}

// 页面元数据配置
const pageMetadata = {
  home: {
    title: {
      en: 'Pan American Jet - Premium Business Aviation Services',
      zh: '泛美公务机 - 尊享私人航空服务'
    },
    description: {
      en: 'Experience luxury business aviation with Pan American Jet. Charter flights, aircraft management, and personalized travel solutions worldwide.',
      zh: '体验泛美公务机尊贵的私人航空服务。全球包机、飞机管理和个性化旅行解决方案。'
    },
    needsUpdate: false  // 默认不更新
  },
  about: {
    title: {
      en: 'About Us - Pan American Jet',
      zh: '关于我们 - 泛美公务机'
    },
    description: {
      en: 'Learn about Pan American Jet\'s history, mission, and commitment to excellence in business aviation.Andy Tong.Xiaozhou.Dido Li',
      zh: '了解泛美公务机的历史、使命和对私人航空卓越品质的承诺。唐子安 Andy Tong.李一宁 Dido Li.周晓舟 Xiaozhou'
    },
    needsUpdate: false
  },
  service: {
    title: {
      en: 'Business Aviation Services - Pan American Jet',
      zh: '私人航空服务 - 泛美公务机'
    },
    description: {
      en: 'Discover our comprehensive range of business aviation services including charter flights, aircraft management, and concierge services.',
      zh: '探索我们全面的私人航空服务，包括包机服务、飞机管理和礼宾服务。'
    },
    needsUpdate: false
  },
  fleet: {
    title: {
      en: 'Our Fleet - Pan American Jet',
      zh: '我们的机队 - 泛美公务机'
    },
    description: {
      en: 'Explore our diverse fleet of private jets available for charter. From light jets to heavy jets, we offer the perfect aircraft for your needs.',
      zh: '探索我们多样化的私人飞机机队。从轻型到重型喷气机，我们提供满足您需求的完美机型。'
    },
    needsUpdate: false
  },
  contact: {
    title: {
      en: 'Contact Pan American Jet - Get in Touch',
      zh: '联系我们 - 泛美公务机'
    },
    description: {
      en: 'Contact Pan American Jet for all your business aviation needs. Our dedicated team is available 24/7 to assist with charter flights and inquiries.',
      zh: '联系泛美公务机，满足您的所有私人航空需求。我们的专业团队全天候为您提供包机服务和咨询服务。'
    },
    needsUpdate: false
  }
}

// 服务详情页的配置
const serviceDetailConfig = {
  lease: {
    needsUpdate: false,    // 默认不更新
    title: {
      en: 'Lease Services - Pan American Jet',
      zh: '租赁服务 - 泛美公务机'
    },
    description: {
      en: 'Discover our premium lease services at Pan American Jet. Experience the highest standards of luxury and safety in business aviation.',
      zh: '探索泛美公务机优质的租赁服务。体验私人航空领域的最高标准和安全性。'
    }
  },
  charter: {
    needsUpdate: false,    // 默认不更新
    title: {
      en: 'Charter Services - Pan American Jet',
      zh: '包机服务 - 泛美公务机'
    },
    description: {
      en: 'Discover our premium charter services at Pan American Jet. Experience the highest standards of luxury and safety in business aviation.',
      zh: '探索泛美公务机优质的包机服务。体验私人航空领域的最高标准和安全性。'
    }
  },
  asset: {
    needsUpdate: false,    // 默认不更新
    title: {
      en: 'Asset Management Services - Pan American Jet',
      zh: '资产管理服务 - 泛美公务机'
    },
    description: {
      en: 'Discover our premium asset management services at Pan American Jet. Experience the highest standards of luxury and safety in business aviation.',
      zh: '探索泛美公务机优质的资产管理服务。体验私人航空领域的最高标准和安全性。'
    }
  }
}

// 机队详情页的配置
const fleetDetailConfig = {
  'G550': {
    needsUpdate: false,    // 默认不更新
    title: {
      en: 'G550 Private Jet - Pan American Jet Fleet',
      zh: 'G550 私人飞机 - 泛美公务机机队'
    },
    description: {
      en: 'Experience luxury travel with our G550 private jet. Discover the perfect blend of comfort, performance, and style.',
      zh: '体验我们的 G550 私人飞机带来的奢华旅行。探索舒适、性能和风格的完美结合。'
    }
  }
}

// 配置你的网站路由
const routes = [
  {
    path: '/',
    priority: '1.0',
    changefreq: 'daily',
    images: ['/res/imgs/home/<USER>', '/res/imgs/about/ceo.jpg', '/res/imgs/about/office1.jpg'],
    ...pageMetadata.home
  },
  {
    path: '/about',
    priority: '0.8',
    changefreq: 'weekly',
    images: ['/res/imgs/about/key.jpg', '/res/imgs/about/ceo.jpg', '/res/imgs/about/p1.jpg', '/res/imgs/about/office1.jpg'],
    ...pageMetadata.about
  },
  {
    path: '/service',
    priority: '0.8',
    changefreq: 'weekly',
    images: ['/res/imgs/service/key.jpg'],
    ...pageMetadata.service
  },
  {
    path: '/fleet',
    priority: '0.8',
    changefreq: 'weekly',
    images: ['/res/imgs/fleet/key.jpg'],
    ...pageMetadata.fleet
  },
  {
    path: '/contact',
    priority: '0.8',
    changefreq: 'weekly',
    images: ['/res/imgs/contact/key.jpg'],
    ...pageMetadata.contact
  }
]

// 服务详情页的 ID 列表
const serviceDetailIds = [
  'lease',           // 租赁服务
  'charter',         // 包机服务
  'asset'            // 资产管理
]

// 机队详情页的 ID 列表
const fleetDetailIds = [
  'G550'             // 湾流G550
]

// 动态路由配置
const dynamicRoutes = [
  // 服务详情页
  ...serviceDetailIds.map(id => ({
    path: `/service/detail/${id}`,
    priority: '0.7',
    changefreq: 'weekly',
    images: ['/res/imgs/service/key.jpg'],
    ...serviceDetailConfig[id as keyof typeof serviceDetailConfig]
  })),
  // 机队详情页
  ...fleetDetailIds.map(id => ({
    path: `/fleet/detail/${id}`,
    priority: '0.7',
    changefreq: 'weekly',
    images: ['/res/imgs/fleet/key.jpg'],
    ...fleetDetailConfig[id as keyof typeof fleetDetailConfig]
  }))
]

// 获取页面的最后修改时间
function getLastModifiedTime (path: string, timestamps: Record<string, string>): string {
  // 移除开头的斜杠以匹配路由配置
  const routePath = path.replace(/^\//, '')

  // 检查是否是动态路由
  const isDynamicRoute = path.includes('/detail/')
  let metadata

  if (isDynamicRoute) {
    const [section, , id] = routePath.split('/')
    if (section === 'service') {
      metadata = serviceDetailConfig[id as keyof typeof serviceDetailConfig]
    } else if (section === 'fleet') {
      metadata = fleetDetailConfig[id as keyof typeof fleetDetailConfig]
    }
  } else {
    metadata = pageMetadata[routePath as keyof typeof pageMetadata]
  }

  // 如果页面配置为需要更新，返回当前时间
  if (metadata?.needsUpdate) {
    return new Date().toISOString()
  }

  // 如果页面不需要更新，返回存储的时间戳或当前时间（如果没有存储的时间戳）
  return timestamps[path] || new Date().toISOString()
}

function generateSitemap () {
  const timestamps = getTimestamps()
  const allRoutes = [...routes, ...dynamicRoutes]

  // 更新时间戳
  const newTimestamps = {...timestamps}
  allRoutes.forEach(route => {
    const lastmod = getLastModifiedTime(route.path, timestamps)
    newTimestamps[route.path] = lastmod
  })

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd
        http://www.google.com/schemas/sitemap-image/1.1
        http://www.google.com/schemas/sitemap-image/1.1/sitemap-image.xsd
        http://www.google.com/schemas/sitemap-news/0.9
        http://www.google.com/schemas/sitemap-news/0.9/sitemap-news.xsd">
  ${allRoutes
    .map(
      (route) => `
  <url>
    <loc>${BASE_URL}${route.path}</loc>
    <lastmod>${getLastModifiedTime(route.path, timestamps)}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
    ${route.images ? route.images.map(image => `
    <image:image>
      <image:loc>${BASE_URL}${image}</image:loc>
      <image:title>${route.title?.en || 'Pan American Jet'}</image:title>
      <image:caption>${route.description?.en || 'Pan American Jet - Premium Private Aviation Services'}</image:caption>
    </image:image>
    <image:image>
      <image:loc>${BASE_URL}${image}</image:loc>
      <image:title>${route.title?.zh || '泛美公务机'}</image:title>
      <image:caption>${route.description?.zh || '泛美公务机 - 尊享私人航空服务'}</image:caption>
    </image:image>`).join('') : ''}
  </url>`
    )
    .join('')}
</urlset>`

  writeFileSync(resolve(__dirname, '../public/sitemap.xml'), sitemap)

  // 保存新的时间戳
  saveTimestamps(newTimestamps)

  console.log('Sitemap generated successfully!')
}

generateSitemap()
