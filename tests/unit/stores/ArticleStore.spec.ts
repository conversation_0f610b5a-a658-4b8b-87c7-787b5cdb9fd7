import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useArticleStore } from '@/stores/article'

describe('Article Store', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例并使其处于激活状态
    setActivePinia(createPinia())
  })

  it('初始状态正确', () => {
    const store = useArticleStore()
    expect(store.articles).toEqual([])
    expect(store.currentArticle).toBeNull()
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })

  it('fetchArticles 成功后更新文章列表', async () => {
    const store = useArticleStore()
    
    // 模拟获取文章列表
    await store.fetchArticles()
    
    // 验证结果
    expect(store.articles.length).toBeGreaterThan(0)
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })

  it('featuredArticles getter 返回精选文章', async () => {
    const store = useArticleStore()
    
    // 模拟获取文章列表
    await store.fetchArticles()
    
    // 验证精选文章结果
    expect(store.featuredArticles.length).toBeGreaterThan(0)
    expect(store.featuredArticles.every(article => article.featured)).toBe(true)
  })
}) 