// 测试首页
describe('首页', () => {
  beforeEach(() => {
    // 访问首页
    cy.visit('/')
  })

  it('显示正确的标题', () => {
    // 检查页面是否包含期望的文本
    cy.contains('h1', '首页')
  })

  it('导航到文章页面', () => {
    // 点击浏览文章按钮
    cy.contains('浏览文章').click()
    
    // 检查 URL 是否改变
    cy.url().should('include', '/articles')
    
    // 检查文章页面标题
    cy.contains('h1', '文章列表')
  })

  it('显示精选文章', () => {
    // 检查精选文章区域是否存在
    cy.contains('h2', '精选文章')
    
    // 等待文章加载完成
    cy.get('.article-card').should('have.length.greaterThan', 0)
  })
}) 