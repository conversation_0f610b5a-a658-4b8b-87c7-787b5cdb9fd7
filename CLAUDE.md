# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + TypeScript website for Pan American Jet, a private aviation company. It's built with modern technologies including Vite, TailwindCSS, Element Plus, and supports multiple languages (English, Spanish, Portuguese, Arabic, Chinese Traditional/Simplified).

## Key Architecture

**Frontend Stack:**
- Vue 3 with Composition API and TypeScript
- Vite for build tooling with SSG (Static Site Generation)
- TailwindCSS + Element Plus for UI components
- Pug templates for cleaner HTML syntax
- SCSS for custom styling with variables and mixins
- Pinia for state management
- Vue Router with module-based route organization

**Internationalization:**
- Vue-i18n with dynamic language loading from external JSON files
- Language files in `pre-task/i18n/` and `src/i18n/langs/`
- Custom i18n plugin for merging language files
- Element Plus locale configuration for UI components

**Project Structure:**
- `src/views/` - Page components with nested routing modules
- `src/components/` - Reusable components
- `src/stores/` - Pinia stores for state management
- `src/composables/` - Vue 3 composition functions
- `src/i18n/` - Internationalization setup and language management
- `src/router/modules/` - Route definitions organized by feature
- `tests/` - Separate test directory with unit and e2e tests

## Common Development Commands

```bash
# Development
npm run dev                    # Start development server on port 8916
npm run type-check            # Run TypeScript type checking
npm run lint                  # Run ESLint
npm run format                # Format code with Prettier

# Testing
npm run test:unit             # Run unit tests with Vitest
npm run test:unit:coverage    # Run unit tests with coverage report
npm run test:e2e              # Run Cypress e2e tests
npm run test:e2e:dev          # Run Cypress in development mode

# Build and Deploy
npm run build                 # Build for production with SSG
npm run preview               # Preview production build
npm run generate-sitemap      # Generate sitemap.xml
```

## Development Guidelines

**Template Language:**
- Uses Pug templates (`<template lang="pug">`) for cleaner syntax
- TailwindCSS classes in Pug: replace `:` with `_` (e.g., `md:flex` → `md_flex`, `w-1/2` → `w-1_2`)
- Element Plus components auto-imported via unplugin

**Styling:**
- SCSS files organized in `src/assets/scss/` with separate files for components and views
- CSS variables for theme switching (light/dark mode)
- BEM naming convention for custom classes
- TailwindCSS for utility classes

**State Management:**
- Pinia stores for global state
- Language store manages i18n state and dynamic language loading
- User store for authentication state

**Routing:**
- Modular route organization in `src/router/modules/`
- SEO meta tags managed through route guards
- SSG pre-rendering for static pages

**Testing:**
- Vitest for unit testing with Vue Test Utils
- Cypress for e2e testing
- Test files in dedicated `tests/` directory
- Use `data-testid` attributes for reliable element selection

## Key Configuration

- **Vite Config:** Includes SSG, image optimization, legacy browser support, and API proxying
- **Dev Server:** Runs on port 8916 with API proxies to FleetUp services
- **Build Output:** Optimized chunks with terser minification, console removal
- **TypeScript:** Strict type checking with multiple tsconfig files for different environments