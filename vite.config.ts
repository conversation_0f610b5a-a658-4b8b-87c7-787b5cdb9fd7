import {fileURLToPath, URL} from 'node:url'
import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import legacy from '@vitejs/plugin-legacy'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import tailwind from '@tailwindcss/vite'
import postcssPlugin from '@tailwindcss/postcss'
import autoprefixer from 'autoprefixer'
import i18nMergePlugin from './plugins/vite-i18n-plugin'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import viteImagemin from 'vite-plugin-imagemin'

// https://vite.dev/config/
export default defineConfig(({mode}) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  return {
    base: '/',
    plugins: [
      i18nMergePlugin(),
      vue({
        template: {
          compilerOptions: {
            // 处理 Tailwind CSS 中的特殊字符在 pug 中的兼容性问题
            isCustomElement: (tag) => tag.includes('_')
          }
        }
      }),
      vueJsx(),
      vueDevTools(),
      // Legacy浏览器支持
      legacy({
        targets: ['ie >= 11', 'chrome 52', 'safari 10', 'edge >= 79'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        modernPolyfills: true,
        renderLegacyChunks: true,
        polyfills: [
          'es.promise',
          'es.array.iterator',
          'es.object.assign',
          'es.promise.finally',
          'es/map',
          'es/set',
          'es.array.includes',
          'es.string.includes'
        ]
      }),
      viteImagemin({
        gifsicle: {
          optimizationLevel: 7,
          interlaced: false
        },
        optipng: {
          optimizationLevel: 7
        },
        mozjpeg: {
          quality: 60
        },
        pngquant: {
          quality: [0.7, 0.8],
          speed: 4
        },
        svgo: {
          plugins: [
            {
              name: 'removeViewBox'
            },
            {
              name: 'removeEmptyAttrs',
              active: false
            }
          ]
        },
        webp: {
          quality: 70
        }
      }),
      Icons({
        autoInstall: true,
        compiler: 'vue3'
      }),
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto import icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ]
      }),
      Components({
        dirs: [
          'src'
        ],
        extensions: ['vue'],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // Auto register icon components
          IconsResolver({
            prefix: 'i',
            enabledCollections: ['ep']
          })
        ],
        deep: true,
        directoryAsNamespace: true
      }),
      tailwind()
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/assets/scss/variables.scss" as *;
            @use "@/assets/scss/element/index.scss" as *;
          `
        }
      },
      postcss: {
        plugins: [
          postcssPlugin(),
          autoprefixer()
        ]
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@res': fileURLToPath(new URL('public/res', import.meta.url)),
        '@data': fileURLToPath(new URL('public/data', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0',
      port: 8916,
      proxy: {
        '/api': {
          target: 'https://online.dev.fleetup.net/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/trunk': {
          target: 'https://trunk.fleetup.net',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/szdev': {
          target: 'https://s.szdev.fleetup.cc',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/szdev/, '')
        }
      }
    },
    build: {
      outDir: './dist',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.time', 'console.timeEnd']
        }
      },
      rollupOptions: {
        input: {
          main: fileURLToPath(new URL('./index.html', import.meta.url))
        },
        output: {
          // 全局变量名称映射，相当于webpack的externals中的globals配置
          globals: {},
          // JS文件输出配置
          chunkFileNames: (chunkInfo) => {
            return 'static/js/[name]-[hash].js'
          },
          entryFileNames: 'static/js/[name]-[hash].js',
          // 资源文件输出配置
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') ?? []
            const ext = info[info.length - 1]

            // CSS 文件
            if (/\.(css)$/i.test(assetInfo.name ?? '')) {
              return 'static/css/[name]-[hash].[ext]'
            }

            // 图片文件
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name ?? '')) {
              return 'static/imgs/[name]-[hash].[ext]'
            }

            // 字体文件
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name ?? '')) {
              return 'static/fonts/[name]-[hash].[ext]'
            }

            // 其他资源文件
            return 'static/[name]-[hash].[ext]'
          }
        }
      }
    },
    assetsInclude: ['**/*.jpg', '**/*.png', '**/*.svg', '**/*.webp'],
    ssr: {
      // 在 SSR 期间不要外部化这些依赖
      noExternal: ['element-plus']
    },
    ssgOptions: {
      script: 'async',
      formatting: 'minify',
      crittersOptions: {
        // 禁用 critters，因为它可能会导致一些问题
        preload: 'js-lazy',
        preloadFonts: true,
        inlineFonts: false
      },
      includedRoutes(paths, routes) {
        // 明确指定要预渲染的路由
        const routesToPrerender = [
          '/',
          '/about',
          '/service',
          '/fleet',
          '/safety',
          '/licenses',
          '/contact'
        ]
        
        console.log('Available paths:', paths)
        console.log('Routes to prerender:', routesToPrerender)
        
        return routesToPrerender
      },
      onRouteRendered(route, html, appCtx) {
        console.log(`✓ Pre-rendered: ${route}`)
      },
      onFinished() {
        console.log('✓ SSG build completed!')
      }
    }
  }
})

// 生成站点地图
async function generateSitemap() {
  const { SitemapStream, streamToPromise } = await import('sitemap')
  const { Readable } = await import('stream')
  const { writeFile } = await import('fs/promises')
  const { resolve } = await import('path')

  const links = [
    { url: '/', changefreq: 'daily', priority: 1 },
    { url: '/about', changefreq: 'monthly', priority: 0.8 },
    { url: '/services', changefreq: 'weekly', priority: 0.9 },
    { url: '/contact', changefreq: 'monthly', priority: 0.8 },
    // 添加其他路由...
  ]

  const stream = new SitemapStream({ hostname: 'https://your-domain.com' })
  const data = await streamToPromise(Readable.from(links).pipe(stream))
  await writeFile(resolve(__dirname, 'dist/sitemap.xml'), data)
}
